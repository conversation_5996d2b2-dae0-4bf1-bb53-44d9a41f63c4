{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "9f4aadfa-568f-40fe-a425-f0dc5287d135", "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"site3303": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"sourceMap": true, "outputPath": "dist/site3303", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["src/polyfills.ts", "src/process.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/sitemap.xml", "src/robots.txt", "src/nbbps2c5bjrdcq1mr6dx88cswomk6p.html"], "styles": ["src/styles.scss", "node_modules/ngx-sharebuttons/themes/modern.scss", "node_modules/ngx-sharebuttons/themes/default.scss"], "scripts": [], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.prod.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "site3303:build:production"}, "development": {"buildTarget": "site3303:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "site3303:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["src/polyfills.ts", "src/process.ts"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/sitemap.xml", "src/nbbps2c5bjrdcq1mr6dx88cswomk6p.html"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}