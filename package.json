{"name": "site3303", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build && npm run sentry:sourcemaps", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org stani<PERSON>-lys<PERSON> --project 3303-site-test ./dist/site3303 && sentry-cli sourcemaps upload --org stanislav-lysenko --project 3303-site-test ./dist/site3303"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/google-maps": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@angular/service-worker": "^18.2.13", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/free-brands-svg-icons": "^6.1.2", "@fortawesome/free-solid-svg-icons": "^6.1.2", "@ng-bootstrap/ng-bootstrap": "^17.0.0", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^7.0.0", "@sentry/angular": "^8.41.0", "@sentry/cli": "^2.45.0", "angular-google-tag-manager": "1.10.0", "assert": "^2.0.0", "bootstrap": "^5.3.2", "crypto-browserify": "^3.12.0", "crypto-js": "^4.1.1", "https-browserify": "^1.0.0", "js-sha1": "^0.6.0", "jspdf": "^3.0.0", "luxon": "^3.5.0", "ng-circle-progress": "^1.7.1", "ng-recaptcha-2": "^14.0.0", "ngx-cookie-service": "^18.0.0", "ngx-mask": "^16.4.2", "ngx-sharebuttons": "^11.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "rxjs": "~7.4.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tslib": "^2.3.0", "zone.js": ">=0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.13", "@angular-eslint/builder": "^19.1.0", "@angular-eslint/eslint-plugin": "^19.1.0", "@angular-eslint/eslint-plugin-template": "^19.1.0", "@angular-eslint/schematics": "^19.1.0", "@angular-eslint/template-parser": "^19.1.0", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.13", "@sentry/webpack-plugin": "^2.22.6", "@types/crypto-js": "^4.1.2", "@types/jasmine": "~3.10.0", "@types/luxon": "^3.4.2", "@types/node": "^12.20.55", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.28.0", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": ">=5.5.4"}}