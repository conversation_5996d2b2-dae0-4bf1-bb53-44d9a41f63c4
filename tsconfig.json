/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "es2022",
    "lib": [
      "es2022",
      "dom"
    ],
    "paths": {
      "crypto": [
        "./node_modules/crypto-browserify"
      ],
      "stream": [
        "./node_modules/stream-browserify"
      ],
      "assert": [
        "./node_modules/assert"
      ],
      "http": [
        "./node_modules/stream-http"
      ],
      "https": [
        "./node_modules/https-browserify"
      ],
      "os": [
        "./node_modules/os-browserify"
      ]
    },
    "useDefineForClassFields": false
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
