name: Dev Deployment
on:
  push:
    branches:
      - develop
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Execute deploy commands on server
        id: deploy
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script_stop: true
          script: |
            cd /root/www/sources/dev
            git pull
            cd /root/www
            docker-compose build dev
            docker-compose stop dev
            docker-compose rm -f dev
            docker-compose up -d --no-deps --force-recreate dev
