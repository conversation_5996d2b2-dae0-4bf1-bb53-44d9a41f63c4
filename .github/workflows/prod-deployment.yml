name: Prod Deployment
on:
  workflow_dispatch:
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Execute deploy commands on server
        id: deploy
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script_stop: true
          script: |
            cd /root/www/sources/site
            git pull
            cd /root/www
            docker-compose build site
            docker-compose stop site
            docker-compose rm -f site
            docker-compose up -d --no-deps --force-recreate site
