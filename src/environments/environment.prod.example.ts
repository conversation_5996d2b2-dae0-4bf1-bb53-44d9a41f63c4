import {Environment, selectDomain} from "../app/shared/utils/select-domain";

export enum env{
  localhost = 'http://127.0.0.1:8000/',
}

export const domains: Environment[] = [
  {
    server: env.localhost,
    domain: 'http://localhost:4200',
    id: '3303'
  }
];

export const environment = {
  production: true,
  url: selectDomain(),
  firebaseConfig: {
    apiKey: '',
    authDomain: '',
    databaseURL: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
    measurementId: ''
  },
  storageServiceKey: 'secret-key'
};
console.log(`---------URL: ${environment.url}-------`);
console.log(`----------PRODUCTION MODE: ${environment.production}----------------`);
