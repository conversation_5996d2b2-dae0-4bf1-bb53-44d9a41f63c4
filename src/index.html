<!DOCTYPE html>
<html lang="en" translate="no">
<head>
  <meta charset="utf-8">
  <title>Суші 3303 | Доставка японської кухні | Швидка доставка</title>
  <meta name="description" content="Великий вибір оригінальних і дуже смачних суші сетів, доступних для замовлення! З доставкою по місту безкоштовно. Запрошуємо ознайомитись з актуальним меню на сторінці сайту.">
  <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
  <link rel="canonical" href="https://3303.ua/home">

  <base href="/">

  <meta property="og:type" content="website">
  <meta property="og:title" content="Суші 3303 | Доставка японської кухні | Швидка доставка">
  <meta property="og:description" content="Великий вибір оригінальних і дуже смачних суші сетів, доступних для замовлення! З доставкою по місту безкоштовно. Запрошуємо ознайомитись з актуальним меню на сторінці сайту.">
  <meta property="og:url" content="https://3303.ua/">
  <meta property="og:image" content="https://3303.ua/assets/logo.webp">


  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="google-signin-client_id" content="************-tbher5us71us3obvvan9g90ecsuqpmdm.apps.googleusercontent.com">
  <meta name="google-site-verification" content="rxPo2k0tXrLGhOxMSje8Youbq2BE-scmyh20WWyJHhc">
  <link rel="icon" type="image/x-icon" href="assets/favicon.svg">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://accounts.google.com/gsi/client"></script>
  <script async src="https://www.googletagmanager.com/gtag/js?id=GOOGLE-KEY"></script>
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": [
          "Шкільна, 4",
          "Вокзальна, 1",
          "Володимира Великого, 10",
          "Ювілейний, 45б",
          "Кам'янецька, 52/2",
          "Покровська, 20",
          "Сміляньська, 97"
        ],
        "addressLocality": [
          "Запоріжжя",
          "Дніпро",
          "Івано-Франківськ",
          "Харків",
          "Хмельницький",
          "Суми",
          "Черкаси"
        ],
        "postalCode": [
          "69095",
          "49038",
          "76010",
          "61054",
          "29008",
          "40030",
          "18007"
        ],
        "addressCountry": "UA"
      },
      "@id": "https://3303.ua",
      "name": "3303.ua",
      "url": "https://3303.ua",
      "image": "https://3303.ua/assets/logo.webp",
      "logo": "https://3303.ua/assets/logo.webp",
      "openingHours": "Mo-Su 10:00-21:00",
      "priceRange": "UAH",
      "description": "Великий вибір оригінальних і дуже смачних суші сетів, доступних для замовлення! З доставкою по місту безкоштовно. Запрошуємо ознайомитись з актуальним меню на сторінці сайту.",
      "telephone": "(098) 015-33-03"
    }
  </script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GOOGLE-KEY');
  </script>
  <script>
    !function (f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function () {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', 'PIXEL-KEY');
    fbq('track', 'PageView');
  </script>
  <script>
    if (window.location.href.includes('dev.3303')) {
      const metaTag = document.createElement('meta');
      metaTag.name = 'robots';
      metaTag.content = 'noindex';
      document.head.appendChild(metaTag);
    }
  </script>
  <link rel="manifest" href="assets/manifest/manifest.webmanifest.json">
  <script
    src="https://rawcdn.githack.com/zarocknz/javascript-winwheel/229a47acc3d7fd941d72a3ba9e1649751fd10ed5/Winwheel.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/latest/TweenMax.min.js"></script>

<script src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>

</head>
<body>
<noscript><img height="1" width="1" style="display:none"
               src="https://www.facebook.com/tr?id=PIXEL-KEY&ev=PageView&noscript=1"
               alt="facebook-pixel">
  </noscript>
  <div id="skeleton" style="width: 90vw; height: 90vh; display: flex; justify-content: center; position: absolute; z-index: -1; opacity: 0.1;">
    <img width="1000" height="1500" style="width: 100%; height: 100%; object-fit: cover;" src="assets/skeleton.png" alt="skeleton">
  </div>
<div style="display:none;">
  <div id="indexText"></div>
  <div>
    <a href="/home/<USER>">Sets</a>
    <a href="/home/<USER>">Rolls</a>
    <a href="/home/<USER>">Rolls from the chef</a>
    <a href="/home/<USER>">Sushi</a>
    <a href="/home/<USER>">Snacks</a>
    <a href="/home/<USER>">Drinks</a>
    <a href="/home/<USER>">Noodles</a>
    <a href="/about">About Us</a>
    <a href="/contract">Agreement</a>
    <a href="/contacts">Contacts</a>
    <a href="/delivery">Shipping and payment</a>
    <a href="/allergens">Allergens</a>
  </div>
  <div id="products"></div>
</div>
<script>
  const xmlHttp = new XMLHttpRequest();
  xmlHttp.open("GET", 'https://storage.3303.ua/api/app/products?city_id=1&lang=en&platform=ios&limit=99999', true);
  xmlHttp.onload = function() {
    const result = JSON.parse(xmlHttp.responseText);
    const products = document.getElementById('products');
    for (let i = 0; i < result.data.length; i++) {
      const a = document.createElement('a');
      a.innerText = result.data[i].name;
      a.href = window.location.origin + '/home?productId=' + result.data[i].id;
      products.appendChild(a);
    }
  };
  xmlHttp.send(null);
</script>
<app-root></app-root>
</body>
</html>
