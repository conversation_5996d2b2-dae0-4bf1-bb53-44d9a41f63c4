{"BONUSES": {"BONUSES": "Bonuses", "WANT_MORE": "Wont more?", "PAY_WITH_BONUSES": "Pay with bonuses", "WITHOUT_BONUSES": "Bonuses are not accrued", "WITHOUT_BONUSES_YET": "The bonuses have not been credited yet"}, "TRACKING": {"0": "Unable to track", "1": "In processing", "2": "Accepted", "3": "Getting ready", "4": "Handed over for delivery", "5": "Being delivered", "6": "Delivered"}, "TITLE": "Sushi 3303 | japanese food delivery", "TO_CART": "Add to cart", "CURRENCY": "€", "WORK_TIME": "Work time", "FROM": "from", "TO": "to", "ALCOHOL_WARNING": {"YES": "Yes", "NO": "No", "MASSAGE": "Are you over 18 years old?"}, "NAVBAR": {"MAIN": "Main", "CART": "<PERSON><PERSON>", "PROFILE": "Profile", "LANG": "Language", "INFO": "Info", "YOUR_CITY": "Your city", "CHOOSE_CITY": "Select city", "CHOOSE_COUNTRY": "Select country", "FRANCHISE": "Franchise", "SITE_NOT_WORK": "The site is currently down.", "DOWNLOAD_APP": "Download the app!", "NOT_NOW": "Not now", "REPORT": "To report", "REPORT_MASSAGE": "Please let us know about errors on the site. We will be very grateful to you!"}, "FILTER": {"SEARCH": "Search", "INGREDIENTS": "Ingredient", "SORT": "Sort", "CHEAP": "Cheap at first", "EXPENSIVE": "First expensive", "NOVELTY": "Novelty", "RANDOM": "Randomly", "MARKS": "Product type", "ALL": "All", "CLEAR": "Clear"}, "CART": {"YOUR_ORDER": "Your order", "CART_EMPTY": "Cart is empty", "CART_CLEAR": "Clear cart", "ORDER": "Order", "TOTAL": "Total amount", "DISCOUNT_AMOUNT": "Discount amount", "BONUSES": "Bonuses", "CART_WARNING": "Minimum order amount {{minPrice}} {{currency}}. Please, add something to your cart"}, "LANGUAGES": {"UA": "Українська", "EN": "English", "RU": "русский", "ES": "Español"}, "HEADER": {"FREE": "Free", "DELIVERY": "delivery from", "MORE_THAN_JUST_SUSHI": "More than just sushi"}, "LOCATION": {"STREET": "Street", "HOUSE": "House", "ENTRANCE": "Front door", "FLOOR": "Floor", "APARTMENT": "Apartment"}, "PROFILE": {"AUTH_WITH_GOOGLE": "Log in with Google", "DESCRIPTION": "Log in to your account to get all the features. Track an order, view history, manage bonuses and more.", "WELCOME": "Welcome", "BONUS_ACCOUNT": "Bonus account", "NAME": "Name", "EMAIL": "Email", "PHONE": "Phone number", "CHANGE_ADDRESS": "Change address", "CHANGE_DATA": "Change data", "SELECTED_ADDRESS": "Selected address", "MY_ADDRESSES": "My addresses", "ENTRANCE_SMALL": "fr.d.", "FLOOR_SMALL": "fl.", "APARTMENT_SMALL": "ap.", "ADD_ADDRESS": "Add address", "ACHIEVEMENT": "Achievement", "ORDER_TRACKING": "Order Tracking", "DELIVERY_TIME": "Estimated delivery time", "UNABLE_TRACKING": "Can't be tracked", "ORDER_HISTORY": "Order history", "MAKE_ORDER": "Make an order", "CHANGE_PERSON_DATA": "Change personal data", "SAVE": "Save", "NO_ORDERS": "Sorry, you don't have any orders yet", "ORDER_AGAIN": "Order again", "cash": "Cash", "card": "Card", "DELIVERED": "Delivered", "ENTER_PHONE": "Please, enter your phone", "ENTER_CODE": "Please, enter code from SMS", "APPROVE_PHONE": "Phone Verification", "CODE_NOT_CORRECT": "Invalid code", "DARK_MODE": "Dark mode", "FAVORITE_PRODUCTS": "Favorite products", "USER_PROFILE": "User profile", "USER_DATA": "User Data"}, "ORDERING": {"YOUR_NAME": "Your name", "PRE-ORDER": "I want to make a pre-order", "PREORDER-TIME": "Choose time", "MY-PREORDER": "Preorder", "WANT_PAY_BY_BONUSES": "I want to pay with bonuses", "HOUSE": "Private house", "YOUR_ADDRESSES": "Your addresses", "ANOTHER_ADDRESS": "Another address", "FREE": "Free", "ACTION_DISCOUNT": "There is a discount for self-delivery. You save", "PER_PIECE": "per piece", "PAID": "To pay", "DELIVERY_METHOD": "Delivery method", "BY_ADDRESS": "By the address", "DELIVERY": "Delivery", "DEPARTMENT": "Department", "SELF_PICKUP": "Self pickup", "DONT_CALL": "Don't call back", "PHONE": "Phone", "COMMENT": "Comment", "COUNT_APPLIANCES": "Number of sticks", "DAY_OF_BIRTHDAY": "It's my birthday", "PAYMENT_METHOD": "Payment method", "CASH": "Cash to courier", "ONLINE": "Online on site", "CARD": "Courier terminal", "FILL_ALL_FIELD": "Please fill in all fields correctly", "SEND_ORDER": "Send an order", "PHONE_PLACEHOLDER": "(XX) XXX XXХ", "STICK_NORMAL_PLACEHOLDER": "Ordinary", "STICK_EDU_PLACEHOLDER": "Training", "WARNING": "Self-visit discount. You save", "PRODUCTS_THAT_MAY_INTEREST_YOU": "Products that may interest you", "CONTINUE": "Continue", "CHEF_RECOMMENDS": "Chef recommends", "ORDER_PLACEMENT": "Order placement", "ORDER_DETAILS": "Order details"}, "ORDER_SUCCESS": {"THANK_YOU": "Thank you for being with us!", "YOUR_ORDER": "Your order has been sent to the operator", "TRACKING": "Track", "ORDER_AGAIN": "Order more", "REDIRECT": "You will be automatically returned to the home page in {{timer}} sec"}, "NOT_FOUND": {"PAGE_NOT_FOUND": "This page was not found", "HOME": "Home"}, "INFORMATION": {"ABOUT": "About us", "DELIVERY": "Shipping and payment", "CONTACTS": "Contacts", "CONTRACT": "Agreement", "INFO_MENU": "Information menu"}, "FOOTER": {"ADDRESS": "Address", "CONTACTS": "Contacts", "LEGAL_INFO": "2015-2024 ©3303® All rights to the content are reserved and prohibited from copying.", "NOT_PAUSE_AND_HOLIDAYS": "Without breaks and days off", "WRITE_TO_THE_DIRECTOR": "Write to the director", "HOW_TO_CONTACT_YOU": "How to contact you?", "CONTACT_PHONE_NUMBER": "Contact phone number", "MESSAGE": "Message", "SEND": "Send", "THANK_YOU_YOUR_REQUEST_HAS_BEEN_SENT": "Thank you! Your request has been sent"}, "PRODUCT_CARD": {"ALLERGENS": "Allergens", "LOOK_IN_INSTAGRAM": "See us on Instagram", "SHARE": "Share on social networks", "NUTRITIONAL_VALUE": "Nutritional value", "PER": "per", "G": "g", "PRODUCT": "of product", "ENERGY_VALUE": "The energy value", "CAL": "calories", "PROTEINS": "<PERSON>teins", "FAT": "Fats", "CARBOHYDRATES": "Carbohydrates", "PIECES": "Pieces"}, "HOME": {"BONUS_COUNT": "Number of bonuses", "ENTER_PRODUCT_NAME": "Enter product name", "SUSHI_3303": "Sushi 3303"}, "CITY": {"ID1": "Zaporozhye", "ID2": "Dnieper", "ID3": "Ivano-Frankivsk", "ID8": "Lviv", "ID9": "Kharkiv", "ID10": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ID11": "<PERSON><PERSON>", "ID14": "<PERSON><PERSON><PERSON><PERSON>", "ID16": "Zaragoza"}, "REVIEWS": {"YOUR_FEEDBACK_IS_GREAT": "YOUR FEEDBACK IS GREAT", "IMPORTANT_FOR_US": "IMPORTANT FOR US", "CHOOSE_THE_MOST_BELOW": "CHOOSE THE MOST BELOW", "A_CONVENIENT_OPTION": "A CONVENIENT OPTION"}, "SET_CONSTRUCTOR": {"TITLE": "Set Constructor", "INFORMATION1": "Welcome to the", "INFORMATION2": "set constructor!", "INFORMATION3": "Here, you can create your own set", "INFORMATION4": "and get a discount for it.", "INFORMATION5": "Please note: the set must consist of no less than", "INFORMATION6": "5 different roles.", "DELETE_PRODUCT": "Remove", "ADD_PRODUCT": "Add", "MODAL_TITLE": "Attention", "SAVE": "Save Set Composition?", "ABOLITION": "Cancel", "CONFIRM": "Confirm"}, "CALLBACK": {"BY_PRESSING": "By pressing", "WAITING_FOR_A_CALL": "Waiting for a call", "PERSONAL_DATA": "you consent to the processing of your personal data", "WE_WILL_CALL_YOU_FOR_FREE": "We will call you for free"}, "ERROR": {"TITLE": "Oops!", "STATUS": "Error", "MESSAGE": "Something went wrong."}}