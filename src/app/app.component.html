<router-outlet></router-outlet>

<ng-template #wheelOfFortuneModal let-modal>
    <div class="modal-header wheel-modal__header">
        <button type="button" (click)="modal.dismiss()">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#E12829" class="bi-x-lg"
                viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                    d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
                <path fill-rule="evenodd"
                    d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
            </svg>
        </button>

    
    </div>
    <div class="modal-body wheel-modal__body">
        <app-wheel-of-fortune>  
        
                @if(!this.storageService.getData('Authorization')){
                <div (click)="signIn()" class="wheel-modal__login wheel-login">
                    <span class="icon"></span>
                    <span class="buttonText" translate="PROFILE.AUTH_WITH_GOOGLE"></span>
                </div>
                }
        </app-wheel-of-fortune>
    </div>
</ng-template>