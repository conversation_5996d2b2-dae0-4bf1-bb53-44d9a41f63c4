import { AfterViewInit, Component, ElementRef, inject, Inject, OnInit, Renderer2, signal, TemplateRef, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from "@angular/router";
import { catchError, filter, Observable, of, switchMap, takeUntil, tap } from "rxjs";
import { GoogleTagManagerService } from "angular-google-tag-manager";
import { changeKeyAnalyticsInHead, setSiteIndex } from "./shared/utils/GTM-detect";
import { StorageLogicService } from "./shared/utils/services/storage-logic.service";
import { Attributes } from "./shared/decoration/interface/decoration";
import { DecorationRestService } from "./shared/decoration/services/decoration-rest.service";
import { DOCUMENT } from "@angular/common";
import { CanonicalService } from "./shared/utils/services/canonical.service";
import { Unsubscribe } from "./unsubscribe";
import { cityNames } from './shared/const/city-names.const';
import { WheelOfFortuneService } from './shared/wheel-of-fortune/services/wheel-of-fortune.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ReCaptchaV3Service } from 'ng-recaptcha-2';
import { ProfileDataservice } from './pages/profile/services/profile-data.service';
import { SentryReplayService } from './shared/sentry-replay/sentry-replay.service';
import { CityDynamicSettingsService } from './shared/cityDynamicSettings/city-dynamic-settings.service';
import { PreorderModalComponent } from './shared/preorder-modal/preorder-modal.component';

declare var gtag: any;

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})

export class AppComponent extends Unsubscribe implements OnInit, AfterViewInit {

  constructor(@Inject(DOCUMENT) private document: Document, private router: Router, private gtmService: GoogleTagManagerService, public storageService: StorageLogicService, public decorationRestService: DecorationRestService, private canonicalService: CanonicalService,
    private readonly renderer: Renderer2, private wheelService: WheelOfFortuneService, private modalService: NgbModal,
    private readonly preorderSettingsService: CityDynamicSettingsService
  ) {
    super();
  }

  title = 'site3303';

  @ViewChild('wheelOfFortuneModal', { static:true }) wheelOfFortuneModal!: TemplateRef<any>;
  @ViewChild('selectPhone', { static: true }) selectPhone!: ElementRef;

  wheelTrophy = this.wheelService.currentWheelTrophy;

  initiateWheelSpin = signal<boolean>(false);

  recaptchaV3Service = inject(ReCaptchaV3Service);

  profileData = inject(ProfileDataservice);

  sentryReplayService = inject(SentryReplayService);

  ngOnInit(): void {
    this.sentryReplayService.monitorReplayOnRoute();

    this.watchInstallEvent();

    this.appendGoogleMapsScript();

    const navEndEvent$ = this.router.events.pipe(
      filter(e => e instanceof NavigationEnd)
    );
    navEndEvent$.subscribe((e: any) => {
      const gtmTag = {
        event: 'page',
        data: e.urlAfterRedirects
      };
      this.gtmService.pushTag(gtmTag);
    });
    this.getCanonical();
    changeKeyAnalyticsInHead()
    setSiteIndex()

    const navEndEvent$1 = this.router.events.pipe(
      filter(e => e instanceof NavigationEnd)
    );
    navEndEvent$1.subscribe((e: any) => {
      gtag('config', this.storageService.getData('cityInfo')?.google_analytics_key ? this.storageService.getData('cityInfo').google_analytics_key : 'G-YM8HVNQD4X', {'page_path': e.urlAfterRedirects});
    });
    this.getDecoration();
  }

  ngAfterViewInit(): void {
      this.openWheelOfFortuneModal();
    this.handlePreorderNotification();
  }

  getDecoration() {
    this.decorationRestService.attributes.subscribe(attributes => {
      const attributeMap = new Map<string, string>();
      attributes.forEach(({attribute, value}) => attributeMap.set(attribute, value));

      const primaryColor = attributeMap.get(Attributes.primaryColor) || '#35753A';
      const secondaryColor = attributeMap.get(Attributes.secondaryColor) || '#656E76';
      const backgroundUrl = attributeMap.get(Attributes.background);

      this.document.documentElement.style.setProperty('--primary', primaryColor);
      this.document.documentElement.style.setProperty('--secondary', secondaryColor);
      document.body.style.backgroundImage = `url('${backgroundUrl}')`;
    });
  }

  getCanonical() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.canonicalService.setCanonicalURL();
    });
  }

  private appendGoogleMapsScript(): void {
    const apiKey = this.storageService.getData('cityInfo')?.google_maps_map_key ?? '';

    const scriptSrc = `https://maps.googleapis.com/maps/api/js?key=${apiKey}`;

    if (this.findExistingGoogleMapsScript()) {
      return;
    }

    let script: HTMLScriptElement = this.renderer.createElement('script');
    script.src = scriptSrc;
    script.async = true;

    this.renderer.appendChild(this.document.head, script);
  }

  private findExistingGoogleMapsScript(): HTMLScriptElement | undefined {
    const srcFragment = 'https://maps.googleapis.com/maps/api/js?key=';
    const googleMapsScript = Array.from(this.document.head.querySelectorAll('script'))
      .find(script => script.src.includes(srcFragment));

    return googleMapsScript;
  }

  private watchInstallEvent(): void {
    window.matchMedia('(display-mode: standalone)').addEventListener('change', (event) => {
      if (event.matches) {
        const currentCity = cityNames[this.storageService.getData('city')];
        this.router.navigate(['/', currentCity, 'menu-pickup']);
      } else {
        this.unregisterServiceWorkerInBrowser();
      }
    });
  }

  private unregisterServiceWorkerInBrowser(): void {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      registrations.forEach(r => {
        if (r.active?.scriptURL.includes('ngsw-worker.js')) {
          r.unregister().then(success => {
            if (success) {
              console.log('Unregistered Service Worker in Browser')
            }
          })
        }
      })
    })
  }

  private openWheelOfFortuneModal() {
    const isUserAuthorized = this.storageService.getData('Authorization');
    const spinsLeft = 2;
    const bonuses = 0;

    this.wheelService.updateWheelConfig(Boolean(isUserAuthorized), spinsLeft, bonuses);
    if (this.wheelOfFortuneModal) {
      this.wheelService.updateWheelModalRef(this.modalService.open(this.wheelOfFortuneModal, { centered: true}))
    }
  }


  private handlePreorderNotification(): void {
    this.preorderSettingsService.getCityDynamicSettings()
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: ({ data }) => {
          const { city: { future_ordering } } = data;
          if (!future_ordering.enabled) {
            this.modalService.open(PreorderModalComponent, { centered: true, size:'lg'})
          }
        },
      })
  }

  public verifyRecaptcha(): Observable<string | null> {
      return this.recaptchaV3Service.execute('importantAction')
        .pipe(
          catchError(() => of(null)),
        )
  }

    signIn() {
      // this.loader = true;
      this.verifyRecaptcha()
        .pipe(
            switchMap(token=>this.profileData.signIn(token)),
            tap(() => {
              if (
                this.profileData.Authorization &&
                !this.profileData.Authorization?.phone
              ) {
                  this.openModal(this.selectPhone, 'sm');
                }
                // this.loader = false;
            }),
          switchMap(() => this.activateWheel()))

        .subscribe();
  }

    private activateWheel(){
      return of(true).pipe(
        tap(()=>this.wheelService.updateWheelModalRef(null)),
        tap(() => this.router.navigate(['/profile'])),
      )
  }

  openModal(content: any, size?: string) {
    let sizeModal = 'lg';
    if (size) {
      sizeModal = size;
    }
    this.modalService.open(content, {
      size: sizeModal,
      keyboard: false,
      backdrop: 'static',
    });
  }
}
