export interface PlacesInterface {
  success: boolean;
  data: PlacesData;
}

export interface PlacesData {
  city: City[];
  all: City[];
}

export interface City {
  id: number;
  name: string;
  address: string;
  coordinates: string;
  city_id: string;
}

export interface SocialMedia {
  success: boolean;
  data: SocialMediaData[]
}

export interface SocialMediaData {
  id: number;
  city_id: number;
  social: string;
  link: string;
  img: string;
  created_at: string;
  updated_at: string;
}


export interface CeoMessageData{
  success: boolean;
  data: CeoMessage[]
}

export interface CeoMessage{
  id: number;
  city_id: number;
  name: string;
  phone: string;
  message: string;
  created_at: string
  updated_at: string;
}
