.footer {
  background: var(--footer-primary);
}
span {
  color: var(--footer-color);
  margin-top: 0.3rem;
  display:block;
  font-size: 13px;
}

.header {
  font-weight: bold;
  margin-bottom: 1.5rem;
  margin-top: 1rem;
}
.margin-mobile {
  margin-bottom: auto;
  margin-top: auto;
}

.social-media {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 0.5rem;

  &__img {
    cursor: pointer;
    width: 2rem;
    height: 2rem;
  }
}

.row {
  background: var(--footer-primary);
}

.payment-icons {
  width: 4rem;
}

.modal-background {
  z-index: 5;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content-access{
  background: var(--footer-primary)!important;
}

.modal-content {
  background-color: var(--footer-secondary);
  width: 600px;
  padding: 20px;
  border-radius: 10px;
  position: relative;
  textarea {
    height: 10em;
    width: 100%
  }
}

.border-for-director-write{
  display: inline-block;
  border: 1px solid #FFFF;
  border-radius: 5px;
}

.border-btn{
  border: 1px solid #0b6035;;
}

.input-phone {
  width: 270px;
  border-radius: 10px;
  border: none;
  padding: 10px;
  margin-right: 20px;

  &::placeholder {
    color: #0b6035;
  }
}

.close {
  position: absolute;
  color: #FFFFFF;
  top: 0;
  right: 10px;
  cursor: pointer;
}



@media screen  and (max-width: 768px){
  .footer-mobile {
    background: var(--footer-primary);
    text-align: center;
    margin-bottom: 3rem;

    &__work-time {
      justify-content: center;
    }
  }
  .margin-mobile {
    margin-bottom: 2rem;
  }
  .social-media {
    justify-content: center;
    margin-bottom: 5rem;
  }
  .col-3 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-2, .col-7, .col-10 {
    flex: 0 0 auto;
    width: 100%;
  }

}

@media screen and (max-width: 480px) {

  .modal-content {
    width: 300px;
    padding: 20px;
    border-radius: 10px;
    position: relative;

    textarea {
      height: 7em;
    }
  }

  .flex-direction {
    flex-direction: column;
  }

  .input-phone {
    width: 260px;
    margin-right: 0;
  }

  .padding-top{
    padding-top: 10px;
  }
}
