.body {
  display: flex;
  flex-direction: column;
  min-height: 100vh
}

.flex-1 {
  flex: 1;
}

.sticky-menu {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
  margin: 0 auto;
  border-radius: 0 0 20px 20px;
  transition: all .5s ease-in-out;
  padding: 1rem;

  &__shadow {
    background: #ffffff;
    box-shadow: 0 29px 28px #00000040;
  }
}

@media screen  and (max-width: 768px){
  .sticky-menu {
    position: static;
    margin: 0!important;
  }
}

@media screen  and (max-width: 480px){
  .sticky-menu{
    padding-bottom: 0;
  }
}
