
<div class="enable-text" *ngIf="countryCityService?.cityInfo?.message">{{ countryCityService.cityInfo.message }}</div>

<div class="enable-text" *ngIf="countryCityService?.cityInfo?.work_time === false && countryCityService?.cityInfo?.enabled === 1">
  <span translate="NAVBAR.SITE_NOT_WORK"></span><span>&nbsp;</span><span
  translate="WORK_TIME"></span><span> {{ countryCityService.cityInfo.work_start }}
  - {{ countryCityService.cityInfo.work_end }}</span>
</div>

<div class="select-city-mobile">
  <img class="logo" [src]="logoStyle?.value ? logoStyle?.value : themeLogicService.isDarkTheme ? 'assets/logo.webp' : 'assets/logo.webp'" alt="Logo image" routerLink=""
       loading="lazy" width="100" height="82">
  <div class="d-flex align-items-center flex-column">
    <button style="font-size: 14px" class="grey-button mt-2" (click)="openChooseCityModal(cities)">
      <span *ngIf="countryCityService.selectedCityId === null" translate="NAVBAR.CHOOSE_CITY"></span>
      <span *ngIf="countryCityService.selectedCityId !== null">{{cityName | async}}</span>

    </button>
    <div>
      <span class="small pe-2 grey-font" href="#" translate="WORK_TIME"><span>:</span></span>
      <span class="small pe-2 fw-bold green-font"
            href="#"><span translate="FROM"></span> {{ countryCityService.cityInfo.work_start }} <span
        translate="TO"></span> {{ countryCityService.cityInfo.work_end }}</span>
      <p style="word-break: break-word; width: 14rem"
         class="small pe-2 fw-bold green-font">{{ countryCityService.cityInfo.optional_message }}</p>
    </div>
  </div>
</div>

<div [ngClass]="{'scroll-button--animation__show': scroll > 1550, 'scroll-button--animation__hide': scroll <= 1550}"
     (click)="scrollTop()" class="scroll-button">
  <img class="scroll-img" src="assets/icons/arrow-down-sign-to-navigate.webp" alt="Arrow down sign to navigate"
       loading="lazy" width="19px" height="19px">
</div>

<div style="z-index: 4;" class="navbar-mobile">
  <div class="nav-icon-text-mobile">
    <div class="position-relative nav-icon-mobile">
      <img style="width: 2rem; cursor: pointer" src="assets/icons/home.webp" alt="Home icon" loading="lazy"
           routerLink="">
    </div>
    <span translate="NAVBAR.MAIN"></span>
  </div>
  <div class="nav-icon-text-mobile">
    <div class="position-relative nav-icon-mobile">
      <img style="width: 2rem; cursor: pointer"
           [src]="navigationStyle?.value?.cartIcon ? navigationStyle?.value?.cartIcon : 'assets/icons/cart.webp'"
           alt="Cart icon" loading="lazy"
           (click)="cartToggle()">
      <span *ngIf="cartLogicService.cartLength !== 0"
            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">{{ cartLogicService.cartLength }}</span>
    </div>
    <span translate="NAVBAR.CART"></span>
  </div>
  <div class="nav-icon-mobile nav-icon-text-mobile">
    <button class="profile-button cursor-pointer" *ngIf="profileImg | async as profileImgObj" [class.border-radius-50]="profileImgObj.isRounded"
    (click)="selectProfileNavigationPath()">
      <img class="profile-button__icon icon-mobile" [src]="profileImgObj.url"
        [attr.data-fallbackImg]="defaultProfileImg" alt="Profile icon" loading="lazy"
        appDefaultImg />
    </button>
    <span translate="NAVBAR.PROFILE"></span>
  </div>
  <div class="nav-icon-text-mobile">
    <div ngbDropdown class="d-inline-block nav-icon-mobile">
      <button class="menu nav-icon-mobile" id="dropdownBasic2" ngbDropdownToggle [ngSwitch]="countryCityService.lang$">
        <img *ngSwitchCase="'en'" style="width: 2rem" src="assets/icons/united-kingdom.webp" alt="United Kingdom flag"
             loading="lazy" width="32" height="32">
        <img *ngSwitchCase="'ua'" style="width: 2rem" src="assets/icons/ukraine.webp" alt="Ukraine flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'es'" style="width: 2rem" src="assets/icons/spain.webp" alt="Spain flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'ru'" style="width: 2rem" src="assets/icons/russia.webp" alt="Russia flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'it'" style="width: 2rem" src="assets/icons/italia.png" alt="Italia flag" loading="lazy"
             width="32" height="32">
      </button>
      <div class="drop-menu" ngbDropdownMenu aria-labelledby="dropdownBasic2">
        <button *ngFor="let object of countryCityService.cityInfo.country.languages" class="drop-menu-button"
                (click)="changeLang(object.code)"
                ngbDropdownItem>
          <div class="d-flex justify-content-sm-between mx-4 align-items-center" [ngSwitch]="object.code">
            <img *ngSwitchCase="'ua'" class="country-img" src="assets/icons/ukraine.webp" alt="Ukraine flag"
                 loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'en'" class="country-img" src="assets/icons/united-kingdom.webp"
                 alt="United Kingdom flag" loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'es'" class="country-img" src="assets/icons/spain.webp" alt="Spain flag" loading="lazy"
                 width="20" height="20">
            <img *ngSwitchCase="'ru'" class="country-img" src="assets/icons/russia.webp" alt="Russia flag"
                 loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'it'" class="country-img" src="assets/icons/italia.png" alt="Italy flag" loading="lazy"
                 width="20" height="20">
            <span class="ms-2">{{ object.name }}</span>
          </div>
        </button>
      </div>
    </div>
    <span translate="NAVBAR.LANG"></span>
  </div>
  <div class="nav-icon-text-mobile">
    <div ngbDropdown class="d-inline-block nav-icon-mobile">
      <button class="menu nav-icon-mobile" id="dropdownBasic3" ngbDropdownToggle>
        <img style="width: 2rem"
             [src]="navigationStyle?.value?.menuIcon ? navigationStyle?.value?.menuIcon : 'assets/icons/menu.webp'"
             alt="Menu icon" loading="lazy">
      </button>
      <div class="drop-menu" ngbDropdownMenu aria-labelledby="dropdownBasic3">
        <button *ngFor="let object of countryCityService.infoPages" class="drop-menu-button" [routerLink]="['../', 'info']"
                [queryParams]="{page: object.site_url}"
                ngbDropdownItem>{{ object.name }}
        </button>
      </div>
    </div>
    <span translate="NAVBAR.INFO"></span>
  </div>
</div>

<nav class="navbar navbar-light navbar-desktop" (click)="$event.stopPropagation()">

  <div class="container d-flex">
    <div class="me-auto">
      <span style="color:  #656E76" class="small pe-2" href="#" [ngStyle]="citySelectorStyles?.value?.cityLabel"
            translate="NAVBAR.YOUR_CITY"></span>
      <button [ngStyle]="citySelectorStyles?.value?.cityName" class="grey-button" (click)="openChooseCityModal(cities)">
        <span *ngIf="countryCityService.selectedCityId === null" translate="NAVBAR.CHOOSE_CITY"></span>
        <span *ngIf="countryCityService.selectedCityId !== null">{{cityName | async}}</span>
      </button>
    </div>
    <a *ngIf="countryCityService.selectedCountry.franchise" class="franchise"
       [ngStyle]="navigationStyle?.value?.franchise" href="https://erp.3303.ua/" target="_blank"
       translate="NAVBAR.FRANCHISE"></a>
    <div ngbDropdown class="d-inline-block">
      <button class="menu" [ngStyle]="navigationStyle?.value?.menuLabel" id="dropdownBasic1" ngbDropdownToggle
              translate="INFORMATION.INFO_MENU">
        <img style="width: 1.7rem"
             [src]="navigationStyle?.value?.menuIcon ? navigationStyle?.value?.menuIcon : 'assets/icons/menu.webp'"
             alt="Menu icon" loading="lazy" width="27" height="27">
      </button>
      <div class="drop-menu" ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <button *ngFor="let object of countryCityService.infoPages" class="drop-menu-button"
                [routerLink]="['../', 'info']" [queryParams]="{page: object.site_url}" ngbDropdownItem>{{ object.name }}
        </button>
      </div>
    </div>
    <div ngbDropdown class="d-inline-block nav-icon-mobile">
      <button class="menu nav-icon-mobile" id="dropdownBasic" ngbDropdownToggle [ngSwitch]="countryCityService.lang$">
        <img *ngSwitchCase="'en'" style="width: 2rem" src="assets/icons/united-kingdom.webp" alt="United Kingdom flag"
             loading="lazy" width="32" height="32">
        <img *ngSwitchCase="'ua'" style="width: 2rem" src="assets/icons/ukraine.webp" alt="Ukraine flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'es'" style="width: 2rem" src="assets/icons/spain.webp" alt="Spain flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'ru'" style="width: 2rem" src="assets/icons/russia.webp" alt="Russia flag" loading="lazy"
             width="32" height="32">
        <img *ngSwitchCase="'it'" style="width: 2rem" src="assets/icons/italia.png" alt="Italia flag" loading="lazy"
             width="32" height="32">
      </button>
      <div class="drop-menu left-8" ngbDropdownMenu aria-labelledby="dropdownBasic2">
        <button *ngFor="let object of countryCityService.cityInfo.country.languages" class="drop-menu-button"
                (click)="changeLang(object.code)" ngbDropdownItem>
          <div class="d-flex justify-content-sm-between mx-4 align-items-center" [ngSwitch]="object.code">
            <img *ngSwitchCase="'ua'" class="country-img" src="assets/icons/ukraine.webp" alt="Ukraine flag"
                 loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'en'" class="country-img" src="assets/icons/united-kingdom.webp"
                 alt="United Kingdom flag" loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'es'" class="country-img" src="assets/icons/spain.webp" alt="Spain flag" loading="lazy"
                 width="20" height="20">
            <img *ngSwitchCase="'ru'" class="country-img" src="assets/icons/russia.webp" alt="Russia flag"
                 loading="lazy" width="20" height="20">
            <img *ngSwitchCase="'it'" class="country-img" src="assets/icons/italia.png" alt="Italia flag" loading="lazy"
                 width="20" height="20">
            <span class="ms-2">{{ object.name }}</span>
          </div>
        </button>
      </div>
    </div>
    <button class="favorite-button cursor-pointer" (click)="navigateToFavoriteProducts()" *ngIf="isAuth | async">
      <svg height="100%" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.427246" width="35.4302" height="35.4302" rx="9" fill="rgb(243, 67, 56)"/>
        <path d="M21.7648 10.1423C20.1988 10.1423 18.6958 10.8486 17.7148 11.9603C16.7338 10.8486 15.2308 10.1423 13.6648 10.1423C10.8883 10.1423 8.71484 12.2481 8.71484 14.938C8.71484 18.2295 11.7748 20.9194 16.4098 24.9957L17.7148 26.1423L19.0198 24.9957C23.6548 20.9194 26.7148 18.2295 26.7148 14.938C26.7148 12.2481 24.5413 10.1423 21.7648 10.1423ZM17.8093 23.7053L17.7148 23.7881L24.9148 18.1423C25.3917 14.9154 25.5898 16.4639 25.5898 13.944C25.5898 12.2045 11.8693 11.8862 13.6648 11.8862C15.0508 11.8862 16.4008 12.7538 16.8733 13.944L25.5898 14.938C26.0668 13.7478 24.1451 10.7138 21.7648 11.8862C23.5603 11.8862 24.9148 13.1985 24.9148 14.938C24.9148 17.4579 22.0888 19.9429 17.8093 23.7053Z" fill="white"/>
        </svg>
    </button>
    <div class="position-relative cursor-pointer">
      <img width="27" height="27"
           [src]="navigationStyle?.value?.cartIcon ? navigationStyle?.value?.cartIcon : 'assets/icons/cart.webp'"
           alt="Cart-icon" loading="lazy"
           (click)="cartToggle()">
      <span *ngIf="cartLogicService.cartLength !== 0"
            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">{{ cartLogicService.cartLength }}</span>
    </div>
    <button class="profile-button margin-left-1 cursor-pointer" *ngIf="profileImg |async as profileDataObj"
      [class.border-radius-50]="profileDataObj.isRounded" (click)="selectProfileNavigationPath()">
      <img class="profile-button__icon" [src]="profileDataObj.url" [attr.data-fallbackImg]="defaultProfileImg"
        alt="Profile icon" loading="lazy" appDefaultImg />
    </button>
  </div>
</nav>


<div [ngClass]="{'show-right': cartLogicService.viewCart, 'hide-right': !cartLogicService.viewCart}" class="cart mt-5">
  <div class="container">
    <div class="row">
      <div class="col-12 mt-3 text-center d-flex justify-content-between">
        <svg style="cursor: pointer" (click)="cartLogicService.viewCart = false" xmlns="http://www.w3.org/2000/svg"
             width="16"
             height="16" fill="currentColor" class="me-4 bi bi-chevron-right" viewBox="0 0 16 16">
          <path fill-rule="evenodd"
                d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
        </svg>
        <span class="green-font" translate="CART.YOUR_ORDER"></span>
        <svg style="cursor: default; opacity: 0" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
             fill="currentColor" class="me-4 bi bi-chevron-right" viewBox="0 0 16 16">
          <path fill-rule="evenodd"
                d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
        </svg>
      </div>
    </div>
  </div>
  <app-cart></app-cart>
</div>

<ng-template #cities>
  <button *ngIf="modalStatus.shouldOpenCountries" class="btn modal-button" (click)="redirectToCountries()">⇦ <span
    class="modal-title" translate="NAVBAR.CHOOSE_COUNTRY"></span></button>
  <svg (click)="closeModal(true)" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
       fill="#E12829" class="modal-button-close bi-x-lg" viewBox="0 0 16 16">
    <path fill-rule="evenodd"
          d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"/>
    <path fill-rule="evenodd"
          d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"/>
  </svg>
  <div class="modal-header none-border mx-auto">
    <span *ngIf="modalStatus.shouldOpenCities" style="font-size: 16px" class="fw-bold accent-text"
          translate="NAVBAR.CHOOSE_CITY"></span>
    <span *ngIf="!modalStatus.shouldOpenCities" style="font-size: 16px" class="fw-bold accent-text"
          translate="NAVBAR.CHOOSE_COUNTRY"></span>
  </div>
  <div class="modal-body row">
    <div *ngIf="!modalStatus.shouldOpenCities" class="d-flex">
      <ng-container *ngFor="let object of countryCityService.countriesList">
        <div class="col-3 text-center mx-auto align-center">
          <button class="btn hover text-color" (click)="applyCountry(object)" dimensions>
            <img *ngIf="object.image" class="d-block mx-auto" style="height: 5rem;" [attr.data-src]="object.image"
                 alt="">
            <img *ngIf="!object.image" class="d-block mx-auto" style="height: 5rem;" src="assets/sushi-like.png" alt="">
            {{ object.name }}
          </button>
        </div>
      </ng-container>
    </div>
    <div *ngIf="modalStatus.shouldOpenCities" class="row">
      <ng-container *ngFor="let object of countryCityService.citiesList">
        <div class="col-3 text-center mx-auto align-center">
          <button class="btn hover" (click)="applyCity(object)" dimensions>
            <img *ngIf="object.img" class="d-block mx-auto" style="height: 5rem;" [attr.data-src]="object.img"
                 alt="Cities image" loading="lazy">
            <img *ngIf="!object.img" class="d-block mx-auto" style="height: 5rem;" src="assets/sushi-like.webp"
                 alt="Sushi like image" loading="lazy">
          </button>
          <span class="d-block text-center mb-3">{{ object.name }}</span>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>

<div *ngIf="countryCityService.cityModule?.bonuses?.status === 1">
  <button [ngClass]="bonusMarkerAnimation" *ngIf="router.url !== '/ordering'"
          [ngStyle]="{background: bonusStyle?.value?.background}"
          class="open-bonus d-flex justify-content-center align-items-center" (click)="openBonusWindow()"
          translate="BONUSES.BONUSES">
    <img class="open-bonus__icon" [src]="bonusStyle?.value?.icon ? bonusStyle?.value?.icon : 'assets/gift-box.webp'" alt="">
  </button>
</div>

<button class="feedback-trigger" (click)="reportModalService.showDefaultReportDialog()">
  <svg fill="#ffffff" width="800px" height="800px" viewBox="-6.31 0 122.88 122.88" version="1.1" id="Layer_1"
    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
    style="enable-background:new 0 0 110.26 122.88" xml:space="preserve" stroke="#ffffff">

    <g id="SVGRepo_bgCarrier" stroke-width="0" />

    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

    <g id="SVGRepo_iconCarrier">
      <style type="text/css">
        .st0 {
          fill-rule: evenodd;
          clip-rule: evenodd;
        }
      </style>
      <g>
        <path class="st0"
          d="M10.69,55.55C5.74,57,3.64,62.09,3.71,67.87c0.03,2.61,0.5,5.35,1.35,8c0.85,2.64,2.07,5.15,3.62,7.31 c3.52,4.93,8.76,7.94,14.9,5.83c0.46-0.16,0.95-0.12,1.36,0.07c0.1-0.04,0.2-0.07,0.31-0.11c1.53-0.5,2.95-0.93,4.24-1.32 c1.44-0.43,2.8-0.82,4.08-1.16c0.99-0.27,2.01,0.32,2.28,1.31c0.02,0.07,0.03,0.14,0.04,0.21c0.18,0.81,0.37,1.62,0.55,2.43 c1.41,6.28,2.77,12.34,4.59,17.26c0.03,0.09,0.27,0.76,0.52,1.43c1.12,3.1,2.33,6.41,3.88,8.36c1.37,1.71,3.26,2.22,6.2-0.08 c0.55-0.52,1.11-1,1.68-1.49c1.75-1.51,3.51-3.01,4.15-5.27c-0.4-0.55-1.17-0.88-1.94-1.21c-2.57-1.09-5.14-2.19-4.45-7.59 c0.03-0.28,0.07-0.53,0.1-0.78c0.2-1.58,0.31-2.39,0.21-2.74c-0.05-0.19-0.44-0.54-1.21-1.25l-0.28-0.26 c-1.52-1.39-2.06-3.38-2.04-5.53c0.02-2.53,0.85-5.31,1.68-7.31c0.26-0.63,0.83-1.04,1.46-1.13l0,0 c19.22-2.66,30.94,0.68,40.26,3.34c0.4,0.11,0.8,0.23,1.19,0.34c-8.32-8.58-15.14-21.58-19.81-34.9 c-4.93-14.07-7.46-28.61-6.8-38.79c-7.62,11.26-20.99,29.14-52.86,40.86c-0.03,0.01-0.06,0.02-0.09,0.03l-0.25,0.09 c-0.33,0.12-0.38,0.09-0.43,0.13c-0.07,0.06,0,0.06-0.13,0.27c-0.02,0.03,0.04-0.07-0.34,0.53C11.49,55.15,11.11,55.42,10.69,55.55 L10.69,55.55L10.69,55.55z M90.07,52.82c0,4.61-4.52,12.9-7.35,13.06c4.82,9.44,10.74,17.61,17.41,22.49 c0.39,0.05,0.75,0.08,1.09,0.06c0.65-0.02,1.29-0.2,1.99-0.64c1.52-1.53,2.48-3.93,2.97-6.96c1.5-9.34-1.56-23.97-6.69-38.05 C94.35,28.67,87.17,15.22,80.46,8.26C78,5.71,75.68,4.11,73.67,3.78c-0.51-0.08-0.8-0.1-0.94-0.05c-0.17,0.06-0.43,0.3-0.84,0.71 c-0.2,0.2-0.4,0.42-0.61,0.66c-0.02,0.06-0.05,0.12-0.07,0.17c-3.14,6.48-2.12,20.47,1.95,35.47l0,0 C76.23,39.3,90.07,40.94,90.07,52.82L90.07,52.82z M99.94,92.08c-0.4,0.08-0.83,0.02-1.21-0.18c-0.35-0.06-0.7-0.14-1.07-0.21 c-2.42-0.5-4.83-1.19-7.43-1.93c-8.81-2.51-19.86-5.66-37.66-3.38c-0.55,1.52-1.01,3.33-1.02,4.94c-0.01,1.16,0.21,2.18,0.83,2.75 l0.28,0.26c1.32,1.2,1.97,1.79,2.3,3.04c0.28,1.04,0.14,2.09-0.13,4.17c-0.03,0.24-0.06,0.5-0.1,0.78 c-0.33,2.62,0.95,3.17,2.23,3.71c1.66,0.71,3.33,1.42,4.15,3.69c0.14,0.33,0.19,0.71,0.12,1.09c-0.75,3.84-3.13,5.89-5.52,7.94 c-0.53,0.46-1.06,0.91-1.56,1.38l0,0c-0.04,0.04-0.08,0.07-0.12,0.11c-5.2,4.16-8.77,2.99-11.5-0.42 c-1.95-2.44-3.26-6.04-4.48-9.41c-0.1-0.29-0.21-0.57-0.52-1.42c-1.89-5.1-3.28-11.31-4.72-17.73l-0.15-0.65 c-0.72,0.2-1.43,0.41-2.13,0.62c-1.45,0.43-2.84,0.86-4.17,1.29c-0.17,0.05-0.28,0.1-0.37,0.14c-0.87,0.35-1.26,0.5-2.04,0.14 c-7.65,2.23-14.04-1.46-18.32-7.45C3.89,82.86,2.49,79.99,1.53,77C0.57,74.01,0.04,70.89,0,67.9c-0.08-7.1,2.62-13.44,8.94-15.68 c0.31-0.51,0.49-0.79,0.88-1.12c0.44-0.37,0.8-0.5,1.55-0.77l0.24-0.09c0.03-0.01,0.06-0.02,0.09-0.03 C44.41,38.17,56.97,19.42,64.11,8.75c2-2.98,3.59-5.36,5.16-6.93c0.77-0.77,1.36-1.26,2.16-1.56c0.83-0.31,1.64-0.33,2.82-0.14 c2.86,0.46,5.87,2.45,8.88,5.57c7.06,7.32,14.55,21.28,19.85,35.84c5.31,14.6,8.47,29.91,6.86,39.89 c-0.62,3.88-1.96,7.03-4.16,9.15l0,0c-0.07,0.07-0.16,0.14-0.25,0.2c-1.4,0.95-2.72,1.32-4.08,1.38 C100.88,92.16,100.41,92.13,99.94,92.08L99.94,92.08z" />
      </g>
    </g>

  </svg>
</button>

<div *ngIf="countryCityService.cityInfo?.enabled === 1 && countryCityService.cityInfo?.work_time === true">
  <button
    class="open-callback d-flex justify-content-center align-items-center" [ngStyle]="{background: callbackStyle?.value?.background}"
    (click)=" openModal()">
    <img class="callback"
         [src]="callbackStyle?.value?.icon ? callbackStyle?.value?.icon : '../../../../../assets/icons/callback-icon.webp'"
         alt="callback-icon"></button>
</div>

<div [ngClass]="bonusWindowAnimation" id="mydiv"
     *ngIf="returnBonuses() && !router.url.includes('/ordering') && Authorization && countryCityService.cityModule?.bonuses?.status === 1"
     class="bonus d-flex justify-content-between align-items-center">
  <button class="bonus-close" (click)="closeBonusWindow()">X</button>
  <div id="mydivheader" class="d-flex w-100">
    <div class="d-flex align-items-center justify-content-center flex-column w-25">
      <span class="bonus__title" translate="BONUSES.BONUSES"><span>:</span></span>
      <span
        class="bonus__quantity">{{ (returnBonuses().bonus_value || 0).toFixed(2) }}{{ countryCityService.selectedCountry.currency }}</span>
    </div>
    <div class="d-flex w-75 flex-column mx-4">
      <div>
        <ngb-progressbar style="height: 5px" class="w-100" type="secondary" [max]="cartLogicService.maxBonus"
                         [value]="cartLogicService.activeBonus"></ngb-progressbar>
      </div>
      <div class="d-flex justify-content-between align-items-center">
        <span class="me-2">0%</span>
        <span class="green-font fw-bold fs-16">{{ cartLogicService.activeBonus }}%</span>
        <span class="ms-2">{{ cartLogicService.maxBonus }}%</span>
      </div>
      <button routerLink="../info" [queryParams]="{page: 'bonus'}" (click)="closeBonusWindow()"
              class="bonus-button none-border" translate="BONUSES.WANT_MORE"></button>
    </div>
  </div>
</div>


<div *ngIf="showModal" class="modal-background d-flex flex-column w-100 h-100">
  <div class="disclaimer">
    <span translate="CALLBACK.BY_PRESSING" class="me-1"></span>
    <span>"</span><span class="color-span" translate="CALLBACK.WAITING_FOR_A_CALL"></span> <span>"</span>, <span
    translate="CALLBACK.PERSONAL_DATA"></span>.
  </div>
  <div class="modal-content">
    <span class="close" (click)="closeCallBackModal()">&times;</span>
    <div class="main-content d-flex align-items-center justify-content-between">
      <p class="m-0 color-span text-light" translate="CALLBACK.WE_WILL_CALL_YOU_FOR_FREE"></p>
      <input type="text" class="input-phone mb-0" placeholder="{{countryCityService.placeholder}}"
        mask="{{countryCityService.mask}}" [(ngModel)]="phoneNumber"
        (focus)="onInputFocus()">
      <button [disabled]="phoneNumber.length !== (countryCode.length + phoneLength)" (click)="postCallBackRequest()"
              class="btn btn-success" translate="CALLBACK.WAITING_FOR_A_CALL"></button>
    </div>
  </div>
</div>
