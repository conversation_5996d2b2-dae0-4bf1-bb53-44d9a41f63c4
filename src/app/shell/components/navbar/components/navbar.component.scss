img {
  margin-left: 1rem;
}

.fs-16 {
  font-size: 16px;
}

.left-8 {
  left: -8rem !important;
}

.logo {
  margin-right: 1rem;
  width: 25%;
  cursor: pointer;
  object-fit: contain;
}

.market {
  width: 8rem;
}

.phone {
  width: 10rem;
}

.grey-button {
  color: var(--primary);
  font-weight: bold;
  border: none;
  border-radius: 2rem;
  display: inline-block;
  line-height: 1.5em;
  padding: 0.2em 1em 0.4em 1em;
  margin: 0;
  background-color: var(--select);
  width: 14rem;
  transition: all .5s ease;

  &:hover {
    background-color: var(--input-background-hover);
  }
}

.hover {
  border: none;
  transition: all .4s ease;

  &:hover {
    transform: scale(1.1, 1.1);
  }
}

.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 10rem;
  width: 80%;
}

.cursor-pointer {
  cursor: pointer;
}

.favorite-button {
  border:none;
  background-color: transparent;
  padding: 0;
  margin-left: 25px;
  height: 34px;
}

.profile-button {
  border: none;
  background-color: transparent;
  padding: 0;
  height: 29px;
  aspect-ratio: 1/1;

  &__icon {
    height: 100%;
    margin: 0;
    object-fit: cover;
  }
}

.border-radius-50 {
  border-radius: 50%;
  overflow: hidden;
}

.margin-left-1 {
  margin-left: 1rem;
}

.margin-top-4 {
  margin-top: 4rem;
}

.none-border {
  border: none;
}

.franchise {
  border-radius: 2rem;
  background-color: transparent;
  padding: 0.2em 1em 0.4em 1em;
  border: 2px solid #ECEFF0;
  color: #656E76;
  transition: all .3s ease;

  &:hover {
    background: var(--primary);
    border: 2px solid #245029;
    color: white!important;
  }
}

.menu {
  color: #656E76;
  border: none;
  border-radius: 2rem;
  display: inline-block;
  line-height: 1.5em;
  background-color: transparent;
}

.search {
  select {
    color: #c0304a;
  }
}

.cart {
  border-radius: 20px;
  height: 100%;
  width: 20rem;
  position: fixed;
  background: #FFFFFF;
  box-shadow: 0 29px 28px rgba(0, 0, 0, 0.25);
  right: -100%;
  z-index: 4;
}

.dropdown-menu {
  --bs-dropdown-link-active-bg: var(--primary);
  --bs-dropdown-link-active-color: var(--primary);
}

.drop-menu {
  background: #FFFFFF;
  box-shadow: 0 9px 10px rgb(0 0 0 / 11%);
  border-radius: 0 0 20px 20px;
  border: none;
  left: 1rem;
  top: 3rem;
}

.dropdown-menu {
  width: 13rem;
  font-size: 13px;
}

.country-img {
  margin: 0;
  width: 1.3rem;
  height: 1.3rem;
}

drop-men {
  transition: all 1s ease-in-out;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: var(--primary);
  padding: 4px 0;
  box-shadow: none;
  transition: all .2s ease;
}

.drop-menu-button:hover {
  width: calc(100% - 2rem);
  background: var(--primary);
  color: #FFFFFF;
  border-radius: 10rem;
  margin: 1rem;
  transition: all .5s ease;
}

.small-red-font {
  color: #c0304a;
  font-size: 12px;
}

.green-font {
  color: var(--primary);
  font-weight: bold;
}

.clear-card {
  font-size: 12px;
  color: #656E76;
  border: none;
  border-radius: 2rem;
  padding: 0.2em 1em 0.4em 1em;
  background-color: #ECEFF0;

}

.counter {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
  outline: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.enable-text {
  text-align: center;
  background: #c0304a;
  color: #FFFFFF;
  padding-bottom: 1rem;
  padding-top: 1rem;
}

.select-city-mobile {
  display: none;
}

.navbar-mobile {
  display: none;
}

.align-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scroll-img {
  margin: 0;
  transform: rotate(180deg);
  width: 1.2rem;
}

.scroll-button {
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  bottom: 3rem;
  border-radius: 100rem;
  position: fixed;
  z-index: 3;
  display: flex;
  box-shadow: 0 0 7px 3px rgb(0 0 0 / 13%);
  left: 93%;
  transform: translate(-50%, 0);
  background: #ffffff;
  align-items: center;
  justify-content: center;
  transition: all .4s ease;

  &:hover {
    .scroll-img {
      filter: invert(1);
    }

    background: var(--primary);
  }

  &--animation {
    &__show {
      left: 93%;
    }

    &__hide {
      left: 150%;
    }
  }
}

.bonus {
  z-index: 9999999;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--select);
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  border: none;
  min-width: 30rem;
  width: 15%;
  height: 6rem;

  &__quantity {
    font-size: 20px;
    font-weight: bold;
    color: #c0304a;
  }

  &__title {
    font-size: 16px;
  }

  &-close {
    color: #FFFFFF;
    border: none;
    border-radius: 10rem;
    width: 2rem;
    height: 2rem;
    background-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 100%;
    top: 0;
    margin-left: 1rem;
  }

  &-button {
    z-index: 2;
    margin: .5rem;
    color: white;
    background: var(--primary);
    border-radius: 10rem;
    transition: all .4s ease;

    &:hover {
      background: #0f8549;
    }
  }
}

.bg-danger {
  background: #E12829 !important;
}

.open-bonus {
  z-index: 3;
  padding: 1rem;
  gap: 1rem;
  border: none;
  color: white;
  border-radius: 10rem 0 0 10rem;
  position: fixed;
  right: 0;
  top: 75%;
  transition: all .5s ease-in-out;
  background: #E12829;

  &:hover {
    background: #f63f40;
  }

  &__icon {
    width: 2rem;
    margin: 0;
  }
}

.animation {
  animation: pulse 1s linear infinite;
}

//animation

.hide-bottom {
  bottom: -50% !important;
  transition: all .5s ease-in-out;
}

.show-bottom {
  bottom: 0;
  transition: all .5s ease-in-out;
}

.hide-right {
  right: -100%;
  transition: all .5s ease-in-out;
}

.show-right {
  right: 0;
  transition: all .5s ease-in-out;
}

//callback-modal

.open-callback {
  z-index: 3;
  padding: 10px;
  color: white;
  background: #245029;
  border-radius: 10rem;
  position: fixed;
  right: 0;
  top: 85%;
  transition: all .5s ease-in-out;
  margin-right: 10px;
  margin-left: 10px;
  border: 2px solid #37793e;

  &:hover {
    background: #35753A;
  }
}

.callback{
  width: 20px;
  height: 20px;
  margin: 9px;
}

.phone-img{
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.color-for-span{
  color: #656E76;
}

.modal-background {
  z-index: 5;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.disclaimer {
  background-color: #e7e7e7;
  width: 561px;
  padding: 5px;
  font-size: 12px;
  border-radius: 5px;
  position: relative;
  text-align: center;
}

.color-span {
  font-weight: 800;
  color: var(--text-primary);
}

.input-phone {
  width: 350px;
  border-radius: 10px;
  border: none;
  padding: 10px;
  margin-right: 20px;

  &::placeholder {
    color: #0b6035;
  }

  &:focus {
    border: none;
  }
}

.modal-content {
  background-color: #666E75FF;
  width: 600px;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

.close {
  position: absolute;
  color: #FFFFFF;
  top: 0;
  right: 10px;
  cursor: pointer;
}

.main-content {
  text-align: center;
}

.feedback-trigger {
  position: fixed;
  top: 85%;
  left: 10px;
  z-index: 10000000;
  width: 64px;
  height: 64px;
  border: none;
  border-radius: 100%;
  background-color: var(--danger);
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    width: 20px;
    height: 20px;
  }
}


@media screen and (max-width: 768px) {

  .show-bottom {
    bottom: 2rem;
  }

  .bonus {
    min-width: 21rem;
    bottom: 2rem;

    &-close {
      margin-left: 0;
      left: 90%;
      top: -45%;
    }
  }

  .open-callback {
    top: 85%;
  }
  .open-bonus {
    top: 76%;
  }

  .mt-5 {
    margin-top: 1rem !important;
  }
  .navbar-desktop {
    display: none;
  }
  .dropdown-toggle::after {
    border-right: 0;
    border-bottom: 0;
    border-left: 0;
  }
  .navbar-mobile {
    justify-content: space-evenly;
    align-items: center;
    z-index: 4;
    display: flex;
    position: fixed;
    bottom: 0;
    height: 4.5rem;
    background: var(--block-background);
    width: 100%;
    border-radius: 1.5rem 1.5rem 0 0;
    box-shadow: 0px -7px 26px -3px rgb(0 0 0 / 43%);
  }
  img {
    margin: 0;
  }
  .nav-icon-mobile {
    margin: 0;
    padding: 0;
  }
  .nav-icon-text-mobile {
    margin-top: 3px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #656E76;
    font-size: 12px;

  }
  .drop-menu {
    border-radius: 20px;
  }
  .select-city-mobile {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
  .cart {
    top: 0;
  }
  .scroll-button {
    width: 4rem;
    height: 2rem;
    bottom: 5rem;
    left: 50%;
    position: fixed;
    z-index: 3;
    display: flex;
    border: 4px solid #F0F3F3;
    align-items: center;
    justify-content: center;
    transform: translate(-50%, 0);
    background-color: #F0F3F3;

    &--animation {
      &__show {
        left: 50%;
      }

      &__hide {
        left: 150%;
      }
    }
  }
  .scroll-img {
    filter: opacity(.5);
    transform: rotate(180deg);
    width: 1.2rem;
  }
  .col-3 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .feedback-trigger {
    top: 79%;
  }
}

.scroll-img {
filter: opacity(.5);
transform: rotate(180deg);
width: 1.2rem;
}

.col-3 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.modal-button-close {
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
}

.modal-button {
  position: absolute;
  left: 20px;
  top: 7px;
  font-weight: 700;
  font-size: 16px;
  color: var(--primary);
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .modal-title {
    display: none;
  }
}

@media screen and (max-width: 480px) {
  .feedback-trigger{
    left: auto;
    right: 0;
    border-radius: 1rem 0 0 1rem;
    top: auto;
    bottom: 235px;
    padding: 0;
    width: 45px;
    height: 45px;
  }

  .open-callback {
    position: fixed;
    padding: 2px;
    top: auto;
    bottom: 285px;
    margin: 0;
    border-radius: 1rem 0 0 1rem;
  }

  .open-bonus {
    padding: 0.65rem;
    border-radius: 1rem 0 0 1rem;
    writing-mode: vertical-rl;
    text-orientation: mixed;;
    top: auto;
    bottom: 110px;

    &__icon {
      transform: rotate(90deg);
      width: 1.5rem;
      margin: 0;
    }
  }
  

  .disclaimer {
    background-color: #D8D9DBFF;
    width: 320px;
    padding: 5px;
    font-size: 12px;
    border-radius: 5px;
    position: relative;
    text-align: center;
  }

  .input-phone{
    width: 200px;
    border-radius: 10px;
    border: none;
    padding: 10px;
    margin-right: 0!important;
    margin-bottom: 5px!important;
  }

  .modal-content {
    background-color: #666E75FF;
    width: 300px;
    padding: 20px;
    border-radius: 10px;
    position: relative;
  }

  .main-content {
    flex-direction: column;
    text-align: center;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
