.card-item {
  width: 90%;
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  line-height: 80%;
}
.item {
  font-size: 12px;
  color: var(--text-primary);
}

.green-font {
  color: var(--marker-secondary-color);
  font-weight: bold;
}
.red-font {
  color: #c0304a;
  font-weight: bold;
}
.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 10rem;
  width: 80%;
}
.clear-card {
  font-size: 12px;
  color: var(--button-select-color);
  border: none;
  border-radius: 2rem;
  padding: 0.2em 1em 0.4em 1em;
  background-color: var(--button-select);
}

.img-empty-card {
  display: flex;
  justify-content: center;
}

.sale-price {
  height: 1rem;
  display: flex;
  justify-content: end;
  align-items: center;
  &__sum {
    text-decoration: line-through;
    font-weight: bold;
    color: var(--text-primary)
  }
}

.cart{
  &__items{
    max-height: 50vh;
    overflow-y: auto;
  }

  &__summary{
    padding-top: 24px;
  }
}

.item-actions {
  cursor: pointer;
}