
export interface CartInterface {
  success: boolean;
  data: CartData;
}

export interface CartData {
  id: number;
  products: Products[];
  free_products: Products[];
  bonus: Bonuses;
}

export interface Products {
  id: number;
  name: string;
  quantity: number;
  weight: number | string;
  img: string;
  price?: number;
  regular_price?: number;
  paid_product?: {id: number, price: number};
  gift_product?: { price: number, gift_id: number };
  sale_until?: number;
  valid_until?: number;
  sale_percent?:number
}

export interface BonusesInterface {
  success: boolean;
  data: Bonuses;
}

export interface Bonuses {
  bonus_value: number;
  current_percent: number;
  time_bonus_schedule: TimeBonusSchedule[],
  total_bonus_schedule: TotalBonusSchedule[]
}

export interface TimeBonusSchedule {
  percent: number,
  active: boolean
}

export interface TotalBonusSchedule {
  total_from: number,
  total_to: number,
  percent: number
}
