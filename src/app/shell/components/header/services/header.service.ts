import { Injectable } from '@angular/core';
import {Observable} from "rxjs";
import {HttpClient} from "@angular/common/http";

import {CityInfoInterface} from "../interfaces/city-info.interface";
import {AppApiUrls} from "../../../../app.api.urls";
import {StorageService} from "../../../../utils/services/storage-logic.service";

@Injectable()
export class HeaderService {

  constructor(private http: HttpClient, public storageService: StorageService) { }

  getInfo(): Observable<CityInfoInterface> {
    return this.http.get<CityInfoInterface>(AppApiUrls.cityInfo(), {
      params: {
        city_id: this.storageService.getData('city') || '',
        lang: this.storageService.getData('lang') || ''
      }
    })
  }
}
