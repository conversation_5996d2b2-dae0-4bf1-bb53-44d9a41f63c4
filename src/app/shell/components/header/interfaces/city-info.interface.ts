export interface CityInfoInterface {
  success: boolean;
  data: CityInfoData;
}

export interface CityInfoData {
  id: number;
  enabled: number;
  message: string;
  min_price: number;
  private_key: string;
  public_key: string;
  work_start: string;
  work_end: string;
  work_time: boolean;
  operators: Operators[];
  name: string;
  modules: Modules[];
  country: Country;
  optional_message?: string;
}

export interface Operators {
  city_id: number;
  operator: string;
  phone: string;
  img: string;
}

export interface Modules {
  title: string;
  type: string;
  status: number;
}

export interface Country {
  name: string,
  phone_code: string,
  phone_length: number,
  currency: string,
  franchise: number,
  languages: Languages[]
}

export interface Languages {
  id: number,
  name: string,
  code: string
}
