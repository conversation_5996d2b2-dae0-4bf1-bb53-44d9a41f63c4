.custom-carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 20px;
  padding-bottom: calc((100% * 0.7) / 2.9);
}

.carousel-container {
  position: relative;
  transition: all 0.5s ease;
}

.carousel-cell {
  padding: 0 0.2rem;
  position: absolute;
  width: 70%;
  max-height: 340px;
  background: #FFF;

  img, video {
    width: 100%;
    max-height: 340px;
    border-radius: 20px;
  }
}

.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 20px;

  button {
    background-color: hsla(0, 0%, 100%, .75);;
    color: #333;
    border: none;
    width: 44px;
    height: 44px;
    cursor: pointer;
    outline: none;
    border-radius: 50%;
    font-size: 14px;

    &:hover {
      background-color: #fff;
    }
  }
}

.button-icon {
  height: 60%;
}

.page-line {
  display: flex;
  justify-content: center;
  width: 100%;
  list-style: none;
  text-align: center;
  line-height: 1;
  margin: 0;
  padding-top: 1rem;

  .line {
    display: inline-block;
    width: 3rem;
    height: 5px;
    background: #333;
    opacity: 0.25;
    cursor: pointer;
  }

  .is-selected {
    background: var(--primary);
    opacity: 1;
  }
}

@media screen and (max-width: 768px) {

  .page-line {
    .line {
      width: 2.5rem;
    }
  }
  .carousel-controls {
    padding: 0 3px;
  }
}

.loader {
  color: var(--loader-color);
  font-size: 10px;
  margin: 10rem auto;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  -webkit-animation: load4 1.3s infinite linear;
  animation: load4 1.3s infinite linear;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
