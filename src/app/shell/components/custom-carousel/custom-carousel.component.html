<div class="custom-carousel">
  <div class="carousel-container" [style.transform]="'translateX(' + translateXValue + '%)'">
    <div *ngFor="let product of banners; let i = index" class="carousel-cell" dimensions
         [style.transform]="'translateX(' + getTransformValue(i) + ')'">
      <img *ngIf="!product.video && product.img" class="item" [attr.data-src]="product.img" alt="orange tree"
           (click)="actionOnClick(product)" loading="lazy" aria-hidden="true">
      <video *ngIf="product.video" playsinline autoplay [muted]="true" loop (click)="actionOnClick(product)">
        <source [src]="product.img" type="video/mp4">
      </video>
    </div>
  </div>

  <div class="carousel-controls">
    <button (click)="manualRotate(-1)">
      <svg class="button-icon" viewBox="0 0 100 100">
        <path d="M 10,50 L 60,100 L 70,90 L 30,50  L 70,10 L 60,0 Z" class="arrow"></path>
      </svg>
    </button>
    <button (click)="manualRotate(1)">
      <svg class="button-icon" viewBox="0 0 100 100">
        <path d="M 10,50 L 60,100 L 70,90 L 30,50  L 70,10 L 60,0 Z" class="arrow"
              transform="translate(100, 100) rotate(180) "></path>
      </svg>
    </button>
  </div>
</div>

<ol class="page-line">
  <li class="line" *ngFor="let product of banners; let i = index" [class.is-selected]="selectedPageIndex === i"
      (click)="goToPage(i)"></li>
</ol>
