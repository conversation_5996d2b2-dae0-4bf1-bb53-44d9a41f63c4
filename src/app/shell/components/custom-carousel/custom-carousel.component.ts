import {Component, Inject, Input, OnInit} from '@angular/core';
import {interval, merge, Subject, Subscription, takeUntil} from "rxjs";
import {BannerData} from "../banners/interfaces/banners.interface";
import {DOCUMENT} from "@angular/common";
import {Router} from "@angular/router";
import {Unsubscribe} from "../../../unsubscribe";

@Component({
  selector: 'app-custom-carousel',
  templateUrl: './custom-carousel.component.html',
  styleUrls: ['./custom-carousel.component.scss']
})
export class CustomCarouselComponent extends Unsubscribe implements OnInit {
  selectedPageIndex = 0;
  translateXValue = 15;
  imageSize = 70
  intervalSubscription: Subscription | undefined;
  pauseSlideshow = new Subject<boolean>();

  @Input() banners!: BannerData[] | undefined;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private router: Router) {
    super();
  }

  ngOnInit(): void {
    this.startSlideShow()
    this.setupHoverListeners();
  }

  startSlideShow(): void {
    this.intervalSubscription = interval(4000)
      .pipe(takeUntil(merge(this.pauseSlideshow, this.$destroy)))
      .subscribe({
        next: () => this.rotateArray(1),
      });
  }

  goToPage(index: number): void {
    this.selectedPageIndex = index;
    this.translateXValue = -index * (this.imageSize) + 15;
    if (this.intervalSubscription) {
      this.intervalSubscription.unsubscribe();
    }
    this.startSlideShow();
  }

  actionOnClick(object: BannerData): void {
    if (object.web_action) {
      this.document.location.href = object.web_action;
    } else if (object.product_id) {
      this.router.navigate(['/home', object.site_url], {
        queryParams: {
          productId: object.product_id,
        },
      }).then();
    }
  }

  rotateArray(direction: number): void {
    if (this.banners) {
      const totalItems = this.banners.length;
      this.selectedPageIndex = (this.selectedPageIndex + direction + totalItems) % totalItems;
      this.translateXValue = -this.selectedPageIndex * (this.imageSize) + 15;
    }
  }


  manualRotate(direction: number) {
    this.rotateArray(direction)
    if (this.intervalSubscription) {
      this.intervalSubscription.unsubscribe();
    }
    this.startSlideShow();
  }

  getTransformValue(i: number): string {
    if (this.banners) {
      const lastIndex = this.banners.length - 1;

      if (i === lastIndex && this.selectedPageIndex === 0) {
        return '-100%';
      } else if (i === 0 && this.selectedPageIndex === lastIndex) {
        return `${this.banners.length * 100}%`;
      } else {
        return `${i * 100}%`;
      }
    }
    return '0%';
  }

  setupHoverListeners(): void {
    const carouselContainer = document.querySelector('.carousel-container');

    if (carouselContainer) {
      carouselContainer.addEventListener('mouseenter', () => {
        this.pauseSlideshow.next(true);
      });

      carouselContainer.addEventListener('mouseleave', () => {
        this.pauseSlideshow.next(false);
        this.startSlideShow();
      });
    }
  }

}
