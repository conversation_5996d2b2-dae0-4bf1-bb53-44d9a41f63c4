import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {AppApiUrls} from "../../../../app.api.urls";
import {CategoriesInterface} from "../interfaces/menu.interface";
import {StorageService} from "../../../../utils/services/storage-logic.service";

@Injectable()
export class MenuService {

  constructor(private http: HttpClient, public storageService: StorageService) {
  }

  get(): Observable<CategoriesInterface> {
    return this.http.get<CategoriesInterface>(AppApiUrls.categories(), {
      params: {
        city_id: this.storageService.getData('city')!,
        lang: this.storageService.getData('lang')!
      }
    })
  }
}
