.wheel-modal {
    &__header {
        justify-content: end;
        border-bottom: none;

        button {
            background: none;
            border: none;
        }
    }

    &__body {
        height: 666px;
    }

    &__login {
        display: inline-block;
        background: var(--google-button-background);
        color: var(--google-button-color);
        border-radius: 5px;
        border: thin solid var(--google-button-border);
        white-space: nowrap;

        &:hover {
            cursor: pointer;
        }
        span.icon {
            background: url('https://developers-dot-devsite-v2-prod.appspot.com/identity/sign-in/g-normal.png') transparent 5px 50% no-repeat;
            display: inline-block;
            vertical-align: middle;
            width: 42px;
            height: 42px;
        }

        span.buttonText {
            display: inline-block;
            vertical-align: middle;
            padding-right: 10px;
            font-size: 14px;
            font-weight: bold;
            /* Use the Roboto font that is loaded in the <head> */
            font-family: 'Roboto', sans-serif;
        }
    }
}

