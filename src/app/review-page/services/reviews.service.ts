import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {AppApiUrls} from "../../app.api.urls";
import {ReviewsInterface} from "../interfaces/reviews.interface";

@Injectable({
  providedIn: 'root'
})

export class ReviewsService {
  constructor(private http: HttpClient) {
  }

  get(order_id: number): Observable<ReviewsInterface> {
    return this.http.get<ReviewsInterface>(AppApiUrls.reviews(), {
      params: {
        order_id
      }
    })
  }
}
