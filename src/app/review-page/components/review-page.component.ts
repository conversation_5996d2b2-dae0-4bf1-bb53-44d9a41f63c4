import {Component, OnInit} from '@angular/core';
import {ReviewsService} from "../services/reviews.service";
import {Unsubscribe} from "../../unsubscribe";
import {takeUntil} from "rxjs";
import {ActivatedRoute} from "@angular/router";
import {Channels, ReviewsInterface} from "../interfaces/reviews.interface";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-review-page',
  templateUrl: './review-page.component.html',
  styleUrls: ['./review-page.component.scss']
})
export class ReviewPageComponent extends Unsubscribe implements OnInit {

  orderId!: number
  channelsData!: ReviewsInterface

  constructor(
    private service: ReviewsService,
    private route: ActivatedRoute,
    private translate: TranslateService
  )
  {
    super()
  }

  ngOnInit(): void {
    this.getQueryParams()
    this.get()
  }

  getQueryParams(): void {
    this.route.queryParams
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: value => {
          this.orderId = value['order_id']
        }
      })
  }

  getLink(channel: Channels): string {
    if (channel.channel.type === 'bot') {
      return channel.link + this.orderId
    } else {
      return channel.link
    }
  }

  get(): void {
    this.service.get(this.orderId)
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: value => {
          this.channelsData = value
        }, complete: () => {
          this.translate.use(this.channelsData.data.lang)
        }
      })
  }

}
