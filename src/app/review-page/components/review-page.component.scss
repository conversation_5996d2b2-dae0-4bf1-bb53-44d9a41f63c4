.navbar {
  display: flex;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 0 19px 9px rgb(0 0 0 / 21%);

  &-img {
    width: 4rem;
    margin: .5rem 1rem .5rem;
  }

  &-header {
    font-size: 13px;
    word-break: break-word;
    color: #4a4a4a;
  }
}

.body {
  position: relative;

  &-header {
    font-weight: bold;
    color: #4a4a4a;
  }

  &-icon {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 0 5rem;

    &-header {
      font-weight: bold;
      color: #4a4a4a;
      padding: 0 1rem;
      text-align: center;
    }
  }

  &-img {

    &-icon {
      width: 3rem;
    }

    &-left {
      width: 5rem;
      position: absolute;
      left: -10px;
      top: 0;
    }

    &-right {
      width: 7rem;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}

@media screen and (max-width: 280px) {

  .body {

    &-icon {
      padding: 0 3rem;
    }

    &-img {
      &-left {
        width: 5rem;
        left: -25px;
      }

      &-right {
        width: 7rem;
        right: -23px;
      }
    }
  }
}

@media (prefers-color-scheme: dark) {
  .navbar {
    background: #383838;
    border-bottom: 1px solid #545454;

    &-header {
      color: #FFFFFF;
    }
  }
  .body {
    background: #242424;
    height: 100vh;

    &-header {
      color: #ffffff;
    }

    &-icon {
      &-header {
        color: #ffffff;
      }
    }
  }
}
