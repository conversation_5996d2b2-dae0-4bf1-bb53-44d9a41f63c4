import {ChangeDetectionStrategy, Component, OnInit} from '@angular/core';
import {Router} from "@angular/router";
import {takeUntil} from "rxjs";
import {Unsubscribe} from "../../../unsubscribe";
import {InformationRestService} from "../../information/services/information-rest.service";


@Component({
  selector: 'app-not-found',
  templateUrl: './not-found.component.html',
  styleUrls: ['./not-found.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotFoundComponent extends Unsubscribe implements OnInit {

  constructor(private router: Router, private infoService: InformationRestService) {
    super()
  }

  ngOnInit(): void {
    switch (this.router.url) {
      case '/contact':
        this.infoService.getInfo()
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: value => {
              this.router.navigate(['/contacts'], {
                queryParams: {
                  id: value.data.find(item => item.site_url === 'contacts')?.id
                }
              }).then()
            }
          })
        break;
      case '/about':
        this.infoService.getInfo()
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: value => {
              this.router.navigate(['/about'], {
                queryParams: {
                  id: value.data.find(item => item.site_url === 'about')?.id
                }
              }).then()
            }
          })
        break;
      case '/delivery':
        this.infoService.getInfo()
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: value => {
              this.router.navigate(['/delivery'], {
                queryParams: {
                  id: value.data.find(item => item.site_url === 'delivery')?.id
                }
              }).then()
            }
          })
        break;
      case '/contract':
        this.infoService.getInfo()
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: value => {
              this.router.navigate(['/contract'], {
                queryParams: {
                  id: value.data.find(item => item.site_url === 'contract')?.id
                }
              }).then()
            }
          })
        break;
      case '/seti':
        this.router.navigate(['home/sets']).then()
        break;
      case '/sushi':
        this.router.navigate(['home/sushi']).then()
        break;
      case '/roli':
        this.router.navigate(['home/rolls']).then()
        break;
      case '/roli-vid-shefa':
        this.router.navigate(['home/roll-chef']).then()
        break;
      case '/lokshina':
        this.router.navigate(['home/noodles']).then()
        break;
      case '/zakuski':
        this.router.navigate(['home/snacks']).then()
        break;
      case '/napoi':
        this.router.navigate(['/home/<USER>']).then()
        break;
    }
  }
  redirectToMainPage(): void {
    this.router.navigate(['/home']).then();
  }
}
