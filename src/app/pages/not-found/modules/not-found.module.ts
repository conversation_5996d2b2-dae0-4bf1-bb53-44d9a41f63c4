import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {RouterModule, Routes} from "@angular/router";
import {NotFoundComponent} from "../components/not-found.component";
import {TranslateModule} from "@ngx-translate/core";
import {Shell} from "../../../shell/shell.service";

const routes: Routes = Shell.childRoutes([
  {
    path: '',
    component: NotFoundComponent
  }
])

@NgModule({
  declarations: [NotFoundComponent],
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        TranslateModule
    ]
})
export class NotFoundModule { }
