import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import {HomeRestService} from "../services/home-rest.service";
import {Products} from "../interfaces/home.interface";


export const HomeResolver:ResolveFn<Products|null> = (route: ActivatedRouteSnapshot) => {
  const homeRestService = inject(HomeRestService);
  return homeRestService.getProducts(route.firstChild?.params?.['id']);
}