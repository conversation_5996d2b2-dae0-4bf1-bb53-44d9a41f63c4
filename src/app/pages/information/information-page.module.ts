import {NgModule} from "@angular/core";
import {InformationPageComponent} from "./components/information-page.component";
import {CommonModule} from "@angular/common";
import {RouterModule, Routes} from "@angular/router";
import {TranslateModule} from "@ngx-translate/core";
import {Shell} from "../../shell/shell.service";
import { MapComponent } from "../../shared/map/map.component";
import { ReactiveFormsModule } from "@angular/forms";
import { LocationsDropdownOptionComponent } from "../../shared/locations-dropdown-option/locations-dropdown-option.component";

const routes: Routes = Shell.childRoutes([
  {
    path: '',
    component: InformationPageComponent
  }
])

@NgModule({
  declarations: [InformationPageComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    TranslateModule,
    ReactiveFormsModule,
    MapComponent,
    LocationsDropdownOptionComponent
],
})

export class InformationPageModule {}
