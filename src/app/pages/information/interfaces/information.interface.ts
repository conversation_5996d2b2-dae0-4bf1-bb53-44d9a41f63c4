export interface InformationInterface {
  success: boolean,
  data: InfoData[]
}

export interface InfoData {
  id: number,
  content?: string,
  name: string,
  img: string,
  site_url: string,
  features: InfoFeature[]
}

export interface InfoFeature {
  name: string;
  settings: any;
  data:any
}

export interface InfoZoneData {
  color: string;
  time_from: string;
  time_to: string;
  min_price: number;
  max_min_price: number;
}