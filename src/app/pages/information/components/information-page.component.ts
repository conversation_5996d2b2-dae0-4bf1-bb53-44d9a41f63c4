import {AfterViewInit, Component, DestroyRef, ElementRef, inject, OnInit, Renderer2, signal} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {Observable, debounceTime, filter, map, merge, of, pluck, switchMap, takeUntil, tap, withLatestFrom} from "rxjs";

import {InfoData, InfoFeature, InfoZoneData} from "../interfaces/information.interface";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Meta, SafeUrl, Title} from "@angular/platform-browser";
import {Unsubscribe} from "../../../unsubscribe";
import {StorageLogicService} from "../../../shared/utils/services/storage-logic.service";
import {about, allergens, bonus, common, contacts, contract, delivery} from "../../../shared/utils/meta-tags";
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';
import { FormControl } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LocationApiService } from 'src/app/shared/utils/services/location-api.service';
import { DeliveryLocation, DeliveryZone } from 'src/app/shared/interfaces/delivery-location.interface';
import { InformationRestService } from '../services/information-rest.service';
import { FooterRestService } from 'src/app/shell/components/footer/services/footer-rest.service';

interface CustomData {
  time: string;
  total: number
}

@Component({
  selector: 'app-information-page',
  templateUrl: './information-page.component.html',
  styleUrls: ['./information-page.component.scss'],
})
export class InformationPageComponent extends Unsubscribe implements OnInit, AfterViewInit {
  private readonly destroyRef = inject(DestroyRef);

  private readonly renderer = inject(Renderer2);

  private readonly elementRef = inject(ElementRef);

  private readonly locationApiService = inject(LocationApiService);

  private readonly informationRestService = inject(InformationRestService);

  private readonly sanitizer = inject(DomSanitizer);

  private readonly footerRestService = inject(FooterRestService);

  private selectedLocation = signal<DeliveryLocation | null>(null);

  public activePage!: Observable<InfoData | undefined> | undefined;

  public searchAddressCtrl = new FormControl('', { nonNullable: true });

  public mapUrl = signal<SafeUrl | null>(null);

  public customDeliveryData = signal<CustomData | null>(null);

  public generalDeliveryData = signal<InfoZoneData[]>([])

  public locationsAutocompleteOptions = signal<DeliveryLocation[] | null>(null);

  public suggestPickup = signal<string|null>(null);

  public officeAddress = signal<string | null>(null);

  constructor(
    private activateRoute: ActivatedRoute,
    private title: Title,
    private meta: Meta,
    public storageService: StorageLogicService,
    private countryCityService: CountryCityNotificationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.matchSelectedPage();
    this.observeAddressChanges();
    this.requestZoneData();
  }

  ngAfterViewInit(): void {
    if (this.mapUrl()) {
      const mapRef = this.elementRef.nativeElement.querySelector('.delivery__map') as HTMLIFrameElement;
      this.renderer.setStyle(mapRef.contentDocument?.querySelector('body'), 'margin-top', '-50px')
    }
  }

  private observeAddressChanges() {
    this.searchAddressCtrl.valueChanges
      .pipe(
        tap((val) => {
          if (!val.length || this.selectedLocation()?.address !== val) {
            this.selectedLocation.set(null);
            this.locationsAutocompleteOptions.set(null);
            this.customDeliveryData.set(null);
            this.suggestPickup.set(null)
          };
        }),
        map(val => val.trim()),
        filter(val => val?.length > 2),
        debounceTime(1500),
        switchMap(val => this.locationApiService.getLocationAutocompleteOptions(val)
          .pipe(
            tap((val) => {
              if (val.success) {
                this.locationsAutocompleteOptions.set(val.data?.predictions || []);
              }
            })
          )),

        takeUntilDestroyed(this.destroyRef)
    )
      .subscribe()
  }

  public selectLocation(location: DeliveryLocation) {
    this.selectedLocation.set(location);
    this.searchAddressCtrl.setValue(location.address, {emitEvent:false});
    this.requestLocationDeliveryDetails(location);
    this.locationsAutocompleteOptions.set(null);
  }

  private requestLocationDeliveryDetails(location:DeliveryLocation) {
    this.locationApiService.checkDeliveryCoverageByPlaceId(location.id)
      .pipe(
        filter(res=>res.success),
        tap(res => {
          if (res.data?.available) {
            this.setCustomDeliveryInfo(res.data.zone);
          }
        }),
        map(res=>res.data?.available),
        switchMap(res => {
          return res?of([]):this.getPickupAddressByCity();
        })
      )
      .subscribe()
  }

  private setCustomDeliveryInfo(zone: DeliveryZone | null): void{
    this.customDeliveryData.set(zone ? { time: `${zone.min_time} - ${zone.max_time}`, total: zone.min_price } : null);
  }

  matchSelectedPage(): void {
    this.activePage = merge(
      this.activateRoute.queryParams,
      this.countryCityService.cityChanged$.pipe(withLatestFrom(this.activateRoute.queryParams), pluck('1')),
      this.countryCityService.languageChanged$.pipe(withLatestFrom(this.activateRoute.queryParams),  pluck('1')))
      .pipe(
        takeUntil(this.$destroy),
        tap(value => {
          this.setMetaTags(value['page']);
        }),
        map((value) => {
            return this.countryCityService.infoPages.find(item => item.site_url === value['page']);
      })
    );
  }

  setMetaTags(page:string): void {
    common.forEach(tag => this.meta.updateTag(tag))

    switch (page) {
      case 'contract':
        this.title.setTitle('Швидке та зручне оформлення договору на 3303.ua')
        contract.forEach(tag => this.meta.updateTag(tag))
        break
      case 'contacts':
        this.title.setTitle('Контакти | Суші 3303 | Доставка японської кухні')
        contacts.forEach(tag => this.meta.updateTag(tag))
        break
      case 'delivery':
        this.title.setTitle('Служба доставки 3303: Найшвидша доставка у місті')
        delivery.forEach(tag => this.meta.updateTag(tag))
        break
      case 'allergens':
        this.title.setTitle('Перелік можливих алергенів всієї продукції Суші 3303')
        allergens.forEach(tag => this.meta.updateTag(tag))
        break
      case 'about':
        this.title.setTitle('Суші 3303 | Японська кухня | Інформація про нас')
        about.forEach(tag => this.meta.updateTag(tag))
        break
      case 'bonus':
        this.title.setTitle('Бонусна програма від суші 3303: Отримуйте подарунки')
        bonus.forEach(tag => this.meta.updateTag(tag))
        break

    }
  }

  private requestZoneData(): void {
    this.activePage?.pipe(
      tap(page => {
        if (page?.site_url !== 'delivery' || !page?.features?.length) {
          this.mapUrl.set(null);
          this.generalDeliveryData.set([]);
          this.customDeliveryData.set(null);
        }
      }),
      filter(page => page?.site_url === 'delivery'),
      switchMap((page) => this.informationRestService.getInfo(page?.id)),
      map(res => this.findFeatureByName((res.data as unknown as InfoData).features, 'delivery_map')),
      filter(res => !!res),
      tap(({ settings, data}) => {
        if (settings && settings.iframe_url) {
          this.mapUrl.set(this.sanitizeMapUrl(settings.iframe_url));
        }
        if (data && data.zones) {
          this.generalDeliveryData.set(data.zones)
        }
        if (data && data.places) {
          this.officeAddress.set(data.places[0].address);
        }
      })
    ).subscribe();
  }

  private sanitizeMapUrl(url:string): SafeUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  private findFeatureByName(features: InfoFeature[], name:string): InfoFeature|undefined {
    return features.find(feature => feature.name === name);
  }

  private getPickupAddressByCity():Observable<string> {
    return this.footerRestService.getPlaces()
      .pipe(
        map(res => res.data.city.map(c => c.address).join(",")),
        tap(res=>this.suggestPickup.set(res))
      )
  }
}
