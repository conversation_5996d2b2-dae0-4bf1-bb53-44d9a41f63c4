.green-font {
  color: var(--text-title);
  font-size: 20px;
}

:host ::ng-deep .grey-font h2 {
  font-size: 18px !important;
}

:host ::ng-deep .grey-font h3 {
  font-size: 16px !important;
}

:host ::ng-deep .grey-font h4 {
  font-size: 14px !important;
}

:host {
  img {
    max-width: 100%;
  }
}

span {
  color: var(--text-primary);
  display: block;
  font-size: 14px;
}


        .green-button {
          color: var(--button-color);
          background: var(--button-primary);
          border-radius: 1rem;
          padding-top: .6rem;
          padding-bottom: .6rem;
          transition: all .4s ease;

          &:hover {
            background: var(--button-primary-hover);
          }
        }

:host ::ng-deep .grey-font img:not(ul img) {
  max-width: 100%;
  height: auto;
}

.delivery-block{
  p {
    margin-bottom: 0;
  }

  display: flex;
  align-items: center;
  width: 100%;
}

.delivery{
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 49px 36px;
  margin-block: 24px;
  border-radius:22px;
  color: #656E76;
  transition: all 0.3s ease;
  box-shadow: 0px 4px 25px 5px rgba(0, 0, 0, 0.25);
  background-color: var(--block-background);

  @media (prefers-color-scheme:dark) {
    background-color: var(--dark-theme-primary-background);
  }

  &__custom {
    flex: 1 1 calc(40% - 24px);
    order:1;
    position: relative;

    &-header{
      font-size: 16px;
      font-weight: 700;
    }

    &-icon{
      position: absolute;
      transform: translateY(50%);
      left: 17px;

    }

    &-section{
      display: flex;
      flex-direction: column;
      gap: 27px;
      order: 2;
    }

    &-block{
      @extend .delivery-block;
      max-width: 90%;
      font-weight: 700;

      svg {
        margin-right:8px;
      }
    }

    &-value {
      margin-left: auto;
      font-weight: 700;
      color: var(--text-title) !important;
      display: flex;

      span {
        font-size: inherit;
        color:inherit !important;
      }
    }

    &-options{
      position: absolute;
      z-index:10;
      box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
      background-color: var(--block-background);
      display: flex;
      flex-direction: column;
      border-radius: 6px;
      padding-bottom: 12px;
      width: 100%;

      @media (prefers-color-scheme:dark) {
        background-color: var(--dark-theme-primary-background);
      }
    }

    &-option{
      border: none;
      background-color: transparent;
      padding-inline: 14px;

      app-locations-dropdown-option {
        display: block;
        padding-block: 8px;
        border-bottom: 1px solid #C4C4C4;
      }

      &:hover {
        background-color: rgba(18, 169, 93, 0.11);
      }
    }
  }

  &__input {
    color: var(--input-color) !important;
    border: none;
    border-radius: 8px;
    padding: 0.2em 1em 0.4em 1em;
    background-color: var(--input-background);
    width: 100%;
    max-width: 600px;
    height: 56px;
    margin-bottom: 24px;
    padding-left: 60px;
    font-weight: 700;


    &:hover {
      background-color: var(--input-background-hover);
    }
  }

  &__map-container{
    display: flex;
    flex-direction: column;
    gap: 10px;
    order: 2;
    flex: 1 1 60%;
  }

  &__map {
    border-radius: 21px;
    height: 480px;
    display: inline-block;
    overflow: hidden;
    position: relative;
    border:1px solid #656E76;
    margin-bottom: 17px;

    iframe{
      position: absolute;
      bottom: 0;
      left: 0;
      border: none;
      pointer-events: none;
    }
  }

  &-zone {
    @extend .delivery-block;
    gap: 4px;
    flex-wrap: wrap;

    &__color{
      --zone-bcg:green;
      width: 41px;
      height: 41px;
      background-color: var(--zone-bcg);
      border-radius: 5px;
    }
  }

}

:host ::ng-deep .location .address__route {
  text-wrap: wrap!important;
}

.marker{
  width: 2rem;
  height: 2rem;
}


@media (max-width:992px){
  .delivery{
    padding: 13px 12px 21px;

    &__map-container{
      order:1
    }

    &__map{
      height: 178px;

      iframe {
        top: -150px;
      }
    }

    &__custom {
      order: 2;
      flex-basis: 100%;
    }
  }
}

@media (max-width: 768px) {
  :host ::ng-deep .grey-font img {
    max-width: 90%;
    margin: 0 auto;
  }

  .icon-marker{
    display: none!important;
  }
}
