<div class="d-flex justify-content-center align-items-center my-5" *ngIf="!activePage">
  <div class="lds-spinner"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
</div>
<div class="container mt-4">
  <h1 class="green-font fw-bold">{{(activePage | async)?.name}}</h1>

  @if (mapUrl()) {
    <div class="delivery">
      <div class="delivery__map-container">
        <div class="delivery__map">
          <iframe [src]="mapUrl()" frameborder="0" width="100%"  height="548"></iframe>
        </div>

        @if(generalDeliveryData().length){
          <div>
            <p>{{"DELIVERY_ZONES.GENERAL_INFO" | translate}}</p>
            <div class="d-flex align-items-center gap-3">
              @for (item of generalDeliveryData(); track $index) {
                <div class="delivery-zone">
                  <div class="delivery-zone__color" [style.--zone-bcg]="item.color"></div>
                  <div class="delivery-zone__info">
                    <p>{{item.time_from}}-{{item.time_to}}</p>
                    <p>{{item.min_price}}₴</p>
                  </div>
                </div>
              }
            </div>
          </div>
        }

      </div>
      <div class="delivery__custom">
        <h3 class="delivery__custom-header">{{'DELIVERY_ZONES.HEADER'|translate}}</h3>
        <p>{{'DELIVERY_ZONES.DESCRIPTION'|translate}}</p>
        <div class="position-relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" fill="currentColor" class="bi bi-search delivery__custom-icon"
               viewBox="0 0 16 16">
            <path
              d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001q.044.06.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1 1 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0" />
          </svg>
          <input [formControl]="searchAddressCtrl" class="delivery__input search" type="search">

          @if(locationsAutocompleteOptions()){
            <div class="delivery__custom-options">
              @for (location of locationsAutocompleteOptions(); track location.id) {
                <button class="delivery__custom-option" (click)="selectLocation(location)">
                  <app-locations-dropdown-option [location]="location"></app-locations-dropdown-option>
                </button>
              }
              @empty {
                <button class="delivery__custom-option">
                  <app-locations-dropdown-option [location]="undefined"></app-locations-dropdown-option>
                </button>
              }
            </div>
          }
        </div>
        @if(suggestPickup() && !customDeliveryData()){
          <p> {{"DELIVERY_ZONES.DELIVERY_UNAVAILABLE" | translate:{ pickup_address:suggestPickup() } }}</p>
        }
        @else if(customDeliveryData()){
          @let data = customDeliveryData();
          <div class="delivery__custom-section">
            <div class="delivery__custom-block">
              <ng-container *ngTemplateOutlet="clockIconRef"></ng-container>
              <p>{{'DELIVERY_ZONES.EXPECTED_DURATION'|translate}}</p>
              <p class="delivery__custom-value">
                {{data?.time}}
              </p>
            </div>
            <div class="delivery__custom-block">
              <ng-container *ngTemplateOutlet="currencyIconRef"></ng-container>
              <p>{{'DELIVERY_ZONES.MIN_ORDER'|translate}}</p>
              <p class="delivery__custom-value">{{data?.total}}&nbsp;<span>₴</span></p>
            </div>
          </div>
        }
        <div class="icon-marker position-absolute bottom-0 left-0 w-100 d-flex align-items-center">
          <img class="marker" src="./assets/icons/marker-icon-green.png" alt="icon-marker">
          <p class="m-0 px-2" >{{ officeAddress() }}</p>
        </div>
      </div>
    </div>
  }

  <div class="grey-font" [innerHTML]="(activePage | async)?.content">
  </div>
  <div class="d-flex align-items-center justify-content-center">
    <a
      *ngIf="(activePage | async)?.site_url === 'referral'"
      class="btn green-button"
      href="/profile">
      {{ 'INFORMATION-PAGE.GO_TO_YOUR_PERSONAL_PROFILE' | translate }}
    </a>
  </div>
</div>

<ng-template #clockIconRef>
  <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.5 19C4.25953 19 0 14.7405 0 9.5C0 4.25953 4.25953 0 9.5 0C14.7405 0 19 4.25953 19 9.5C19 14.7405 14.7405 19 9.5 19ZM9.5 1.32558C4.99302 1.32558 1.32558 4.99302 1.32558 9.5C1.32558 14.007 4.99302 17.6744 9.5 17.6744C14.007 17.6744 17.6744 14.007 17.6744 9.5C17.6744 4.99302 14.007 1.32558 9.5 1.32558Z"
      fill="#42864B" />
    <path
      d="M12.3221 13C12.2055 13 12.089 12.9738 11.9814 12.904L9.20158 11.2901C8.51112 10.8888 8 10.0076 8 9.23119V5.65431C8 5.29662 8.30488 5 8.67253 5C9.04018 5 9.34506 5.29662 9.34506 5.65431V9.23119C9.34506 9.54526 9.61407 10.0076 9.89205 10.1647L12.6718 11.7786C12.9946 11.9618 13.0933 12.3631 12.905 12.6772C12.7705 12.8866 12.5463 13 12.3221 13Z"
      fill="#42864B" />
  </svg>
</ng-template>

<ng-template #currencyIconRef>
  <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.7364 14.2895H8.51825C7.06895 14.2895 5.8936 13.07 5.8936 11.5676C5.8936 11.2053 6.19407 10.9048 6.55639 10.9048C6.91872 10.9048 7.21918 11.2053 7.21918 11.5676C7.21918 12.3365 7.80244 12.9639 8.51825 12.9639H10.7364C11.3108 12.9639 11.788 12.4514 11.788 11.8328C11.788 11.0639 11.5141 10.9137 11.0634 10.7546L7.50197 9.49973C6.81267 9.26112 5.88477 8.74857 5.88477 7.16671C5.88477 5.80577 6.95407 4.70996 8.26198 4.70996H10.4801C11.9294 4.70996 13.1048 5.9295 13.1048 7.43182C13.1048 7.79415 12.8043 8.09461 12.442 8.09461C12.0796 8.09461 11.7792 7.79415 11.7792 7.43182C11.7792 6.66298 11.1959 6.03554 10.4801 6.03554H8.26198C7.68756 6.03554 7.21035 6.5481 7.21035 7.16671C7.21035 7.93554 7.4843 8.08578 7.935 8.24484L11.4964 9.49973C12.1857 9.73833 13.1136 10.2509 13.1136 11.8328C13.1048 13.1848 12.0443 14.2895 10.7364 14.2895Z"
      fill="#42864B" />
    <path
      d="M9.50068 15.4654C9.13836 15.4654 8.83789 15.1649 8.83789 14.8026V4.19795C8.83789 3.83562 9.13836 3.53516 9.50068 3.53516C9.86301 3.53516 10.1635 3.83562 10.1635 4.19795V14.8026C10.1635 15.1649 9.86301 15.4654 9.50068 15.4654Z"
      fill="#42864B" />
    <path
      d="M9.5 19C4.25953 19 0 14.7405 0 9.5C0 4.25953 4.25953 0 9.5 0C14.7405 0 19 4.25953 19 9.5C19 14.7405 14.7405 19 9.5 19ZM9.5 1.32558C4.99302 1.32558 1.32558 4.99302 1.32558 9.5C1.32558 14.007 4.99302 17.6744 9.5 17.6744C14.007 17.6744 17.6744 14.007 17.6744 9.5C17.6744 4.99302 14.007 1.32558 9.5 1.32558Z"
      fill="#42864B" />
  </svg>
</ng-template>
