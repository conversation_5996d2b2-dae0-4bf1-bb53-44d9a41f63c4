import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { catchError, finalize, Observable, of, switchMap, take, takeUntil, tap, throwError } from 'rxjs';
import { ProfileDataservice } from 'src/app/pages/profile/services/profile-data.service';
import { Unsubscribe } from 'src/app/unsubscribe';
import { ProfileRestService } from 'src/app/pages/profile/services/profile-rest.service';
import { ProductData } from 'src/app/pages/home/<USER>/home.interface';
import { FormGroup } from '@angular/forms';
import { ProductDataAdapter } from 'src/app/shared/utils/poduct-data-adapter';
import { StorageLogicService } from 'src/app/shared/utils/services/storage-logic.service';
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';
import { ToastService } from 'src/app/shared/utils/services/toast.service';
import { ACTIVATION_ERROR } from '../activation-error.enum';

@Component({
  selector: 'app-activate-promocode',
  templateUrl: './activate-promocode.component.html',
  styleUrls: ['./activate-promocode.component.scss'],
})
export class ActivatePromocodeComponent extends Unsubscribe implements OnInit {
  @ViewChild('selectPhone') selectPhone!: ElementRef;
  @ViewChild('cities', { static: true }) citiesModal!: TemplateRef<any>;
  @ViewChild('errorToast') errorToast!: TemplateRef<any>;

  promocode: string | null = this.storageService.getSessionData('promocode');

  loader = false;

  public freeProduct: FormGroup | null = null;

  public errorMessage!: string;

  public isAuthorized$? : Observable<boolean>;

  public isOneTimePromo = false;

  constructor(
    public profileData: ProfileDataservice,
    private authService: ProfileDataservice,
    private modalService: NgbModal,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private profileApiService: ProfileRestService,
    private productDataAdapter: ProductDataAdapter,
    private storageService: StorageLogicService,
    protected countryCityService: CountryCityNotificationService,
    public toastService: ToastService
  ) {
    super();
  }

  ngOnInit(): void {
    this.isAuthorized$ = this.authService.isAuthorizedStream.pipe(take(1));
    this.showCitySelectionModalIfCityNotChosen();

    this.subscribeToQueryParams();

    if (this.storageService.getData('city')&& this.promocode) {
      this.profileApiService.getFreeProductPreview(this.promocode).pipe(
        takeUntil(this.$destroy)
      )
      .subscribe((res) => {
        if (res.success && res.data) {
          this.freeProduct = this.generateFormGroupElement(res.data.product);
          this.isOneTimePromo = res.data.onetime === 1
        }
      });
    }
  }

  subscribeToQueryParams() {
    this.activatedRoute.queryParamMap
      .pipe(
        tap((params) => {
          if (!this.promocode) {
            this.promocode = params.get('c');
            this.storageService.setSessionData('promocode', this.promocode);
          }
        }),
        takeUntil(this.$destroy)
      )
      .subscribe();
  }

  authorizedActivatePromo(template:TemplateRef<any>){
    this.activatePromo(template).subscribe();
  }

  signIn(template:TemplateRef<any>) {
    this.loader = true;
    this.authService
      .signIn()
      .pipe(
        tap(() => {
          if (!this.profileData.Authorization?.phone) {
            this.openModal(this.selectPhone, 'sm');
          }
        }),
        switchMap(() => {
          if (this.promocode) {
              return this.activatePromo(template);
          }
          this.toastService.show({
            header: 'ACTIVATE_PROMO.ERROR.HEADER',
            body: 'No code detected',
            classname: 'bg-danger text-light',
            autohide:true
          });
          return of();
        }),
        takeUntil(this.$destroy),
        finalize(() => this.loader = false)
      )
      .subscribe();
  }

  openModal(content: ElementRef, size?: string) {
    let sizeModal = 'lg';
    if (size) {
      sizeModal = size;
    }
   this.modalService.open(content, {
      size: sizeModal,
      keyboard: false,
      backdrop: 'static',
   });
  }

  generateFormGroupElement(item: ProductData): FormGroup {
    return this.productDataAdapter.adaptProductData(item);
  }

  showCitySelectionModalIfCityNotChosen() {
    this.openChooseCityModal(this.citiesModal, false);
  }

  openChooseCityModal(content: TemplateRef<any>, forceOpen: boolean = true) {
    this.countryCityService.performCityCountryFetchRequest().subscribe({
      complete: () => {
        if (
          forceOpen ||
          this.storageService.getData('askToSelectCountry') === '1'
        ) {
          this.storageService.removeData('askToSelectCountry');
          this.modalService.open(content, { size: 'lg', backdrop: 'static' });
        }
      },
    });
  }

  translateError(message: ACTIVATION_ERROR) {
    switch (message) {
      case ACTIVATION_ERROR.NOT_FOUND:
        return 'ACTIVATE_PROMO.ERROR.NOT_FOUND';
      case ACTIVATION_ERROR.SELF_USAGE_RESTRICTED:
        return 'ACTIVATE_PROMO.ERROR.SELF_USAGE_RESTRICTED';
      case ACTIVATION_ERROR.USER_INVITED:
        return 'ACTIVATE_PROMO.ERROR.USER_INVITED';
      case ACTIVATION_ERROR.CODE_USED:
        return 'ACTIVATE_PROMO.ERROR.CODE_USED';
      default:
        return '';
    }
  }

  private activatePromo(template:TemplateRef<any>){
   return this.profileApiService
    .activatePromocode(this.promocode!)
    .pipe(
      tap((res) => {
        if (res?.success) {
          this.router.navigate(['/profile'], {
            queryParams: { c: this.promocode },
            queryParamsHandling: 'merge',
          });
        }
      }),
      catchError((e) => {
        this.errorMessage = this.translateError(e);
        this.toastService.show({
          template,
          header: 'ACTIVATE_PROMO.ERROR.HEADER',
          body: this.errorMessage,
          classname: 'bg-danger text-light',
          autohide:true
        });
        return of();
      }),
    );
  }
}
