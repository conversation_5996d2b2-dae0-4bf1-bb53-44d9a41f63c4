#customBtn {
    display: inline-block;
    background: var(--google-button-background);
    color: var(--google-button-color);
    border-radius: 5px;
    border: thin solid var(--google-button-border);
    white-space: nowrap;
  }
  
  #customBtn:hover {
    cursor: pointer;
  }

#customBtn.button-layout {
    height: 44px;
    width: 262px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

button:disabled {
  &:hover {
    cursor: default;
  }
}

span.icon {
    background: url('https://developers-dot-devsite-v2-prod.appspot.com/identity/sign-in/g-normal.png') transparent 5px 50% no-repeat;
    display: inline-block;
    vertical-align: middle;
    width: 42px;
    height: 42px;
}
  
span.buttonText {
    display: inline-block;
    vertical-align: middle;
    padding-right: 10px;
    font-size: 14px;
    font-weight: bold;
    /* Use the Roboto font that is loaded in the <head> */
    font-family: 'Roboto', sans-serif;
}

.getPromoButton{
  padding: 14px 10px;
}

.promo {
  p {
    margin-bottom: 0;
  }

  gap: 42px;

  @media (min-width:769px) {
    gap: 24px;
  }

  &__product {
    max-width: 457px;
    width: 90%;
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-accent);

    @media (min-width:769px) {
      font-size: 25px;
    }
  }

  &__subtitle {
    font-size: 14px;
    color: var(--text-primary);

    @media (min-width:769px) {
      font-size: 18px;
    }
  }
}

.toast{
  position: fixed;
  top: 0;
  right: 0;
  margin: 0.5em;
  z-index: 1200;
  color: white;
}