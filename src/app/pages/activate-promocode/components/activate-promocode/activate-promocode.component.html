
<div  class="d-flex justify-content-center flex-column align-items-center promo container">
    <app-product-item *ngIf="freeProduct" class="promo__product" [productForm]="freeProduct.value" [isPromoPage]="true"></app-product-item>
    <div class="promo__text" *ngIf="isOneTimePromo; else multiUsePromoText">
        <p class="promo__title" translate="ACTIVATE_PROMO.TITLE"></p>
        <p class="promo__subtitle" translate="ACTIVATE_PROMO.SUBTITLE"></p>
    </div>

   <ng-template #multiUsePromoText>
      <p class="promo__title" translate="ACTIVATE_PROMO.TITLE"></p>
    </ng-template>

    <ng-container *ngIf="isAuthorized$ | async; else signInButton">
      <div (click)="authorizedActivatePromo(dangerTpl)" class="getPromoButton" id="customBtn">
        <span class="getPromoButton__text" translate="ACTIVATE_PROMO.AUTHORIZED_GET"></span>
      </div>
    </ng-container>

    <ng-template #signInButton>
      <button [disabled]="loader" (click)="signIn(dangerTpl)" id="customBtn" [ngClass]="{'button-layout':loader, 'customGPlusSignIn':true}">
          <span class="icon" *ngIf="!loader"></span>
          <span class="spinner-border spinner-border-sm spinner" role="status" aria-hidden="true" *ngIf="loader"></span>
          <span class="buttonText" translate="PROFILE.AUTH_WITH_GOOGLE"></span>
      </button>
    </ng-template>
</div>
<ng-template #dangerTpl> {{errorMessage | translate}}</ng-template>
<app-toasts></app-toasts>

