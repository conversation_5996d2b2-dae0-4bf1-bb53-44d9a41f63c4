import { NgModule } from '@angular/core';
import { CommonModule, NgTemplateOutlet } from '@angular/common';
import { ActivatePromocodeComponent } from '../components/activate-promocode/activate-promocode.component';
import { RouterModule, Routes } from '@angular/router';
import { Shell } from 'src/app/shell/shell.service';
import { TranslateModule } from '@ngx-translate/core';
import { ProductItemModule } from "../../../shared/product-item/product-item.module";
import { NgbToastModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastsContainer } from 'src/app/toastes';

const routes: Routes = Shell.childRoutes([
  {
    path: '',
    component: ActivatePromocodeComponent,
    runGuardsAndResolvers: 'always',
  },
]);

@NgModule({
  declarations: [ActivatePromocodeComponent],
  imports: [CommonModule, RouterModule.forChild(routes), TranslateModule, ProductItemModule, NgbToastModule,
    NgTemplateOutlet, ToastsContainer,],
})
export class ActivatePromocodeModule {}
