import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { map, Observable, takeUntil } from "rxjs";
import { ActivatedRoute, Router } from "@angular/router";
import { Unsubscribe } from "../../../unsubscribe";
import { CityInfoInterface } from "../../../shell/components/header/interfaces/city-info.interface";
import { OrderingRestService } from "../../ordering/services/ordering-rest.service";
import { CartLogicService } from "../../../shell/components/navbar/cart/services/cart-logic.service";

@Component({
  selector: 'app-order-success',
  templateUrl: './order-success.component.html',
  styleUrls: ['./order-success.component.scss']
})
export class OrderSuccessComponent extends Unsubscribe implements OnInit, AfterViewInit {
  @ViewChild('imgRef') imgRef!: ElementRef;

  cityInfo!: CityInfoInterface
  showTrackingButton: boolean = false
  timer = 3
  getQueryParams = this.observeQueryParams();

  constructor(private orderingService: OrderingRestService,
              private cartLogicService: CartLogicService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    super()
  }

  ngOnInit(): void {
    this.cartLogicService.resetCart();
    this.getCityInfo()
    this.redirectToHome();
  }

  ngAfterViewInit() {
    this.scrollToImgRef();
  }

  getCityInfo(): void {
    this.orderingService.getInfo().pipe(takeUntil(this.$destroy))
      .subscribe({
          next: value => {
            this.cityInfo = value
          }, complete: () => {
            if (this.cityInfo.data.modules.find(item => item.type === 'order_tracking')) {
              this.showTrackingButton = true
            }
          }
        }
      )
  }

  redirectToHome(): void {
    const interval = setInterval(() => {
      this.timer -= 1
    }, 1000)
    setTimeout(() => {
      clearInterval(interval)
      this.router.navigate(['/home'], {queryParams: { preorder: null }}).then()
    }, 3000)
  }

  private scrollToImgRef() {
    this.imgRef.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  private observeQueryParams(): Observable<'preorder' |'default'> {
    return this.activatedRoute.queryParamMap.pipe(
      map(params => params.get('preorder') ? 'preorder' : 'default')
    )
  }
}
