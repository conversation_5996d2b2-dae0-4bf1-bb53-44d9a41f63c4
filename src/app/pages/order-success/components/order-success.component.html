<div class="container" *ngIf="getQueryParams | async as orderType">
  @switch(orderType){
    @case('default'){
      <ng-container *ngTemplateOutlet="defaultRef"></ng-container>
    }
    @case('preorder'){
      <ng-container *ngTemplateOutlet="preorderRef"></ng-container>
    }
  }
</div>


<ng-template #defaultRef>
  <img #imgRef style="width: 13%" class="mx-auto d-block my-4" src="../../../assets/sushi-like.webp" alt="Sushi like image" loading="lazy">
  <div class="text-center">
    <span class="d-block fw-bold" translate="ORDER_SUCCESS.THANK_YOU"></span>
    <span style="font-size: 14px" class="d-block" translate="ORDER_SUCCESS.YOUR_ORDER"></span>
    <span translate ="ORDER_SUCCESS.REDIRECT"
      [translateParams]="{timer: timer}"
    ></span>
  </div>
  <div class="text-center mt-3">
<!--    <button *ngIf="showTrackingButton" [routerLink]="'/profile'" translate="ORDER_SUCCESS.TRACKING"></button>-->
    <button routerLink="" class="ms-2" translate="ORDER_SUCCESS.ORDER_AGAIN"></button>
  </div>
</ng-template>

<ng-template #preorderRef>
  <img #imgRef style="width: 13%" class="mx-auto d-block my-4" src="assets/preorder-guy.png" alt="Preorder img" loading="lazy">
  <div class="text-center">
    <span class="d-block fw-bold" translate="PREORDER.SUCCESS_TITLE"></span>
    <span style="font-size: 14px" class="d-block" translate="PREORDER.SUCCESS_SUBTITLE"></span>
  </div>
  <div class="text-center mt-3">
    <button routerLink="" class="ms-2" translate="ORDER_SUCCESS.ORDER_AGAIN"></button>
  </div>
</ng-template>