import { Pipe, PipeTransform } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Pipe({
  name: 'categoryFilter',
  standalone: true
})
export class CategoryFilterPipe implements PipeTransform {

  transform(productFormGroupsArray: AbstractControl[], categoryId:number|null): any {
    if (!categoryId) {
      return productFormGroupsArray;
    }
    const isDiscountCategory = categoryId === 7253;
    if (isDiscountCategory) {
      return productFormGroupsArray.filter(product => product.value.prices[0].sale_percent);
    }
    return productFormGroupsArray.filter(product => product.value.cat_id === categoryId);
  }

}
