<div class="d-flex justify-content-center align-items-center my-5" *ngIf="!products">
    <div class="lds-spinner">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="d-flex justify-content-center sticky-menu" [ngClass]="{'sticky-menu__shadow': isFixed}">
    <div>
        <app-menu [categoriesInput]="categories | async" (activatedCategory)="filterByCategory($event)"
            class="d-block my-4"></app-menu>
        <div class="container filter-container">
            <app-filter [(filter)]="filter" [(filterIng)]="filterIng" [(productSearch)]="productSearch"
                [(valueFilterIng)]="valueFilterIng" [(valueFilterMark)]="valueFilterMark" [(filterByIng)]="filterByIng"
                [(filterByMark)]="filterByMark" [selectedFilter]="selectedFilter" [hideFilterIng]="hideFilterIng"
                [hideFilterMark]="hideFilterMark" (sortProductsByPrice)="sortProductsByPrice($event)">
            </app-filter>
        </div>
    </div>
</div>

<div class="container pb-4" id="gallery-content">
    <h1 class="d-none" translate="HOME.SUSHI_3303"></h1>
    <form [formGroup]="form">
        @if(formArrayElement){
        <div class="row mt-4 card-padding-right-left">
            @for (object of formArrayElement.controls
            | homeSearch : productSearch
            | filterMark : filter
            | filterIng : filterIng
            | categoryFilter: activatedCategoryId(); track object.value.id; let i = $index) {
                <ng-container formArrayName="weight">
                    <div id="products" class="card-padding col-3" *ngIf="object.value as productForm">
                        <form [formGroupName]="i">
                            <app-product-item [productForm]="object.value" [hideCartButton]="true" [isOfflineMenu]="true" (selectWeight)="selectWeight($event, i)"
                                (selectDrink)="selectDrink($event, i)" (openProductModal)="openModal($event)">
                            </app-product-item>
                        </form>
                    </div>
                </ng-container>
            }
        </div>
        }
    </form>
</div>

<ng-template #cardProduct>
    <div class="modal-header none-border">
        <h4 class="modal-title"></h4>
        <button type="button" class="modal__button-close" (click)="close()">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#E12829" class="bi-x-lg"
                viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                    d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
                <path fill-rule="evenodd"
                    d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
            </svg>
        </button>

    </div>
    <div class="modal-body">
        <app-product-card [offlineProductItem]="selectedProduct"></app-product-card>
    </div>
</ng-template>

<ng-template #alcoholWarning>
    <div class="modal-header none-border">
        <h4 class="modal-title"></h4>
        <button type="button" class="modal__button-close" (click)="close()">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#E12829" class="bi-x-lg"
                viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                    d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
                <path fill-rule="evenodd"
                    d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
            </svg>
        </button>

    </div>
    <div class="modal-body text-center">
        <h5 class="grey-font mb-4" translate="ALCOHOL_WARNING.MASSAGE"></h5>
        <button class="warning-yes" (click)="warningSubmit('yes')" translate="ALCOHOL_WARNING.YES"></button>
        <button class="warning-no" (click)="warningSubmit('no')" translate="ALCOHOL_WARNING.NO"></button>
    </div>
</ng-template>