import { Component, HostListener, inject, OnInit, signal, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AsyncPipe, DOCUMENT, NgClass } from '@angular/common';
import { ActivatedRoute, Router, Data } from '@angular/router';
import { NgbDropdownModule, NgbModal, NgbToastModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { BehaviorSubject, combineLatest, fromEvent, map, merge, Observable, startWith, takeUntil, tap } from 'rxjs';
import { GoogleAnalyticsLogicService } from 'src/app/shared/analytics/service/GoogleAnalytics-logic.service';
import { ProductDataAdapter } from 'src/app/shared/utils/poduct-data-adapter';
import { StorageLogicService } from 'src/app/shared/utils/services/storage-logic.service';
import { MenuRestService } from 'src/app/shell/components/menu/services/menu-rest.service';
import { Langs } from 'src/app/shell/components/navbar/interfaces/navbar.interface';
import { FilterByIngInterface, FilterByMarksInterface } from '../home/<USER>/filterByIng.interface';
import { ProductData, FilterType, Prices } from '../home/<USER>/home.interface';
import { TranslateModule } from '@ngx-translate/core';
import { ShareIconsModule } from 'ngx-sharebuttons/icons';
import { DimensionsModule } from 'src/app/shared/directives/dimensions.module';
import { ProductCardModule } from 'src/app/shared/product-card/product-card.module';
import { ProductItemModule } from 'src/app/shared/product-item/product-item.module';
import { HomeModule } from "../home/<USER>/home.module";
import { Unsubscribe } from 'src/app/unsubscribe';
import { NgIf } from '@angular/common';
import { SearchPipe } from '../home/<USER>/search.pipe';
import { MarkPipe } from '../home/<USER>/mark.pipe';
import { IngPipe } from '../home/<USER>/ing.pipe';
import { ShellModule } from "../../shell/shell.module";
import { CategoryFilterPipe } from "./pipes/category-filter.pipe";
import { CategoriesData } from 'src/app/shell/components/menu/interfaces/menu.interface';

@Component({
  selector: 'app-menu-pickup',
  standalone: true,
  imports: [
    FormsModule,
    NgbDropdownModule,
    ReactiveFormsModule,
    NgbTooltipModule,
    ShareIconsModule,
    TranslateModule,
    NgbToastModule,
    ProductCardModule,
    DimensionsModule,
    ProductItemModule,
    HomeModule,
    NgIf,
    ShellModule,
    NgClass,
    CategoryFilterPipe,
    AsyncPipe
  ],
  templateUrl: './menu-pickup.component.html',
  styleUrl: './menu-pickup.component.scss',
  providers: [SearchPipe, MarkPipe, IngPipe]
})
export class MenuPickupComponent extends Unsubscribe implements OnInit {
  products: ProductData[] = [];
  initialProducts: ProductData[] = [];
  sortDirection = ['', FilterType.cheap, FilterType.expensive, FilterType.novelty]
  productId = 0;
  productSearch = '';
  selectedFilter = '';
  filter = '';
  filterIng = '';
  valueFilterIng = '';
  valueFilterMark = '';
  filterByIng: FilterByIngInterface = {};
  filterByMark: FilterByMarksInterface = {};

  shouldSkipHomeNavigation = false;

  hideFilterIng = false;
  hideFilterMark = false;

  appModalLangGoogle = Langs.en;
  appModalLangApple = Langs.en;
  appModalLangScreen = Langs.ua;

  // @ts-ignore
  form: FormGroup;

  @ViewChild('cardProduct') cardProduct: any;
  @ViewChild('alcoholWarning') alcoholWarning: any;

  public isFixed = false;
  
  @HostListener("window: scroll")
  scrollFunction() {
    /**259 is roughly the hight of the sticky header */
    this.isFixed = document.getElementById('gallery-content')!.getBoundingClientRect().top < 259;
  }
  
  document = inject(DOCUMENT);

  selectedProduct: any = null;

  categories = new BehaviorSubject<CategoriesData[]>([])
  
  activatedCategoryId = signal<number | null>(null);

  /* Will remove map=>false later, currently set_constructor is permanently filtered out from the categories list */
  isOnline$: Observable<boolean> = merge(
    fromEvent(window, 'online').pipe(map(() => true)),
    fromEvent(window, 'offline').pipe(map(() => false))
  ).pipe(
    startWith(navigator.onLine),
    map(()=>false)
  );

  constructor(
    private GAService: GoogleAnalyticsLogicService,
    private modalService: NgbModal,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private router: Router,
    public storageService: StorageLogicService,
    private productDataAdapter: ProductDataAdapter,
    private menuRestService: MenuRestService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.initMarksAndIngredients();
    this.firstDataLoad();
    this.handleCategories();
    this.removeSkeleton();
    this.onSortDirection();
  }

  private handleCategories(): void {
    combineLatest([this.isOnline$, this.route.data.pipe(map(data => data['categories'] as CategoriesData[]))])
      .pipe(
        tap(([isOnline, categories]) => {
          if (isOnline) {
            this.categories.next(categories);
          } else {
            const filteredCategories = categories.filter(c => !c.site_url.includes('set_constructor'));
            this.categories.next(filteredCategories)
          }
        }),
        takeUntil(this.$destroy)
      )
      .subscribe();
    }

  private initMarksAndIngredients() {
    combineLatest([this.route.data
      .pipe(
        map((data: Data) => data['marks'])),
      this.route.data
      .pipe(
        map((data: Data) => data['ingredients']))
    ])
    .pipe(takeUntil(this.$destroy))
    .subscribe();
  }

  removeSkeleton(): void {
    const skeleton = this.document.getElementById('skeleton');

    if (skeleton) {
      skeleton.remove();
    }
  }

  filterByCategory(categoryId:number) {
    this.activatedCategoryId.set(categoryId)
  }

  onSortDirection(){
    this.menuRestService.sort$.subscribe((res)=>{
      this.selectedFilter = this.sortDirection[res]
      this.sortProductsByPrice(this.selectedFilter)
    })
  }


  sortProductsByPrice(selectedFilter: string): void {
    this.selectedFilter = selectedFilter;
    const products = [...this.products];
    switch (selectedFilter) {
      case FilterType.cheap:
        this.products = products.sort(
          (a, b) => a.prices[0].price - b.prices[0].price
        );
        break;
      case FilterType.expensive:
        this.products = products.sort(
          (a, b) => b.prices[0].price - a.prices[0].price
        );
        break;
      case FilterType.novelty:
        this.products = products.sort(
          (a, b) => b.prices[0].id - a.prices[0].id
        );
        break;
      case FilterType.random:
        this.selectedFilter = '';
        this.products = this.initialProducts;
        break;
    }
    this.form.setControl(
      'weight',
      this.fb.array(
        this.products.map((item: ProductData) =>
          this.generateFormGroupElement(item)
        )
      )
    );
  }

  warning(): void {
    this.modalService.open(this.alcoholWarning);
  }

  warningSubmit(answer: string): void {
    if (answer === 'yes') {
      this.modalService.dismissAll();
      this.router.navigate(['/drinks']).then();
    } else {
      this.modalService.dismissAll();
      this.router.navigate(['/home']).then();
    }
  }

  private firstDataLoad(): void {
    this.route.data
      .pipe(map((data: Data) => data['products']))
      .subscribe((prodData: ProductData[] | undefined) => {
        this.filterReset();
        this.productsRerender(prodData);
      });
  }

  private productsRerender(prodData: ProductData[] | undefined) {
    if (!prodData || !prodData.length) {
      this.router.navigate(['/home']);
      return;
    }
    this.initialProducts = [...prodData];
    this.products = prodData;
    this.form = this.fb.group({
      weight: this.fb.array(
        this.products.map((item: ProductData) =>
          this.generateFormGroupElement(item)
        )
      ),
    });
    this.sortProductsByPrice(this.selectedFilter)
  }

  generateFormGroupElement(item: ProductData): FormGroup {
    return this.productDataAdapter.adaptProductData(item);
  }

  get formArrayElement(): FormArray | null {
    return this.form ? this.form.get('weight') as FormArray : null;
  }

  selectDrink(prices: Prices, i: number) {
    const { id, weight, price, img, regular_price, sale_percent } = prices;
    const weightItem = this.form.value.weight[i];

    weightItem.weight = id;
    weightItem.weightText = weight;
    weightItem.price = price;
    weightItem.regular_price = regular_price;
    weightItem.sale_percent = sale_percent;
    weightItem.img = img;
  }

  selectWeight(prices: Prices, i: number) {
    this.form.value.weight[i].price = prices.price;
    this.form.value.weight[i].weight = prices.id;
    this.form.value.weight[i].regular_price = prices.regular_price;
    this.form.value.weight[i].sale_percent = prices.sale_percent;
  }

  filterReset(): void {
    this.productSearch = '';
    this.filter = '';
    this.filterIng = '';
    this.valueFilterMark = '';
    this.valueFilterIng = '';
  }

  openModal(productId: number): void {
    const product = this.products.find(
      (item: ProductData) => item.id === productId
    );

    if (product) {
      this.GAService.eventEmitter('view_item', {
        items: [
          {
            item_name: product.name,
            item_id: product.id,
            price: product.prices[0].price,
            quantity: 1,
          },
        ],
      });
    }

    this.selectedProduct = product;
    
    const productModalRef = this.modalService
      .open(this.cardProduct, {
        size: 'xl',
        windowClass: 'test',
        modalDialogClass: 'test',
      })
    productModalRef.result.finally(()=>this.close())
  }

  close(): void {
    this.modalService.dismissAll();
  }

  openApp(url: string): void {
    this.modalService.dismissAll();
    window.open(url, '_blank');
  }
}
