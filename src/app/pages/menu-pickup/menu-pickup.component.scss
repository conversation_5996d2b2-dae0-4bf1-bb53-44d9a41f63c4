.green-button {
    color: var(--button-color);
    background: var(--button-primary);
    border-radius: 1rem;
    padding-top: .6rem;
    padding-bottom: .6rem;
    transition: all .4s ease;

    &:hover {
        background: var(--button-primary-hover);
    }
}

.price {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.market {
    &-android {
        width: 8rem;
        margin-top: 1.5rem;
    }

    &-ios {
        width: 8rem;
        margin-top: 1.5rem;
    }
}

.phone {
    width: 10rem;
}

.warning {
    &-yes {
        width: 3rem;
        border-radius: 1rem;
        padding-top: .6rem;
        padding-bottom: .6rem;
        color: var(--button-color);
        border: none;
        background: var(--button-primary);
    }

    &-no {
        width: 3rem;
        margin-left: 1rem;
        border-radius: 1rem;
        padding-top: .6rem;
        padding-bottom: .6rem;
        color: var(--button-color);
        border: none;
        background: var(--button-danger);
    }
}

.name {
    height: 3rem;
}

.product-card {
    background: var(--block-background);
    box-shadow: var(--block-box-shadow);
    border-radius: 20px;
    border: none;
    transition: all .3s ease;

    &:hover {
        transform: scale(1.03, 1.03);
    }
}

.marking-filter {
    width: 1.8rem;
    height: 1.8rem;
}

.info {
    width: 9%;
    height: 9%;
    margin: 1rem;
}

.drop-menu {
    width: 3.8rem;
    border: 2px solid var(--marker-primary);
    border-radius: 10rem;
    box-shadow: none;
    min-width: 0;
    z-index: 0;
}

.drop-menu-filter {
    background: var(--block-background);
    box-shadow: var(--block-box-shadow);
    border-radius: 20px;
    margin: 1rem 0;
    border: none;
    left: 1rem;
    top: 3rem;
}

.filter-title {
    color: var(--text-primary);
    font-size: 12px
}

.drop-menu-button {
    margin-top: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    color: var(--button-primary);
    padding: 4px 0;
    box-shadow: none;
    transition: all .2s ease;
}

.dropdown-menu {
    width: 15rem;
}

.drop-menu-button:hover {
    width: calc(100% - .4rem);
    background: var(--button-primary-hover);
    color: var(--button-color);
    border-radius: 10rem;
    margin: 1rem .2rem;
    transition: all .5s ease;
}

.none-border {
    border: none;
}

.search {
    color: var(--input-color);
    width: 15rem;
    border: none;
    border-radius: 2rem;
    padding: 0.2em 1em 0.4em 1em;
    background-color: var(--input-background);
    transition: all .3s ease;

    &:hover {
        background-color: var(--input-background-hover);
    }
}

::placeholder {
    font-size: .7em;
    text-align: center;
}

.loader {
    color: var(--loader-color);
    font-size: 10px;
    margin: 10rem auto;
    width: 1em;
    height: 1em;
    border-radius: 50%;
    position: relative;
    text-indent: -9999em;
    -webkit-animation: load4 1.3s infinite linear;
    animation: load4 1.3s infinite linear;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.text-bonus {
    font-size: 12px;
    color: var(--text-primary);
}

@-webkit-keyframes load4 {

    0%,
    100% {
        box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
    }

    12.5% {
        box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }

    25% {
        box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }

    37.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }

    50% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }

    62.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
    }

    75% {
        box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
    }

    87.5% {
        box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
    }
}

@keyframes load4 {

    0%,
    100% {
        box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
    }

    12.5% {
        box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }

    25% {
        box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }

    37.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }

    50% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }

    62.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
    }

    75% {
        box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
    }

    87.5% {
        box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
    }
}

.modal-body {
    border: none;
    border-radius: 0;
}

.modal {
    &__button-close {
        border: none;
        background-color: transparent;
        cursor: pointer;
    }
}

.test {
    border-radius: 1000px;
}

@media screen and (max-width: 768px) {

    .ingredients {
        display: none;
    }

    .bonuses {
        flex-direction: column;
    }

    .text-bonus {
        display: none;
    }

    .product-button {
        font-size: .9rem;
    }

    .card-padding {
        padding: .4rem;
    }

    .card-padding-right-left {
        padding-left: .4rem;
        padding-right: .4rem;
    }

    .filter-mobile {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .col-3 {
        flex: 0 0 auto;
        width: 50%;
    }

    .col-4 {
        flex: 0 0 auto;
        width: 100%;
    }

    .mt-4 {
        margin-top: 0 !important;
    }
}

@media screen and (max-width: 1200px) {
    .filter-title {
        display: block;
        margin-bottom: .5rem;
    }

    .product-img {
        width: 10rem;
    }
}

@media (min-width: 1200px) and (max-width: 1400px) {
    .container.filter-container {
        max-width: 1400px;
    }
}

.modal-header::ng-deep {
    display: flex;
    justify-content: space-between;
}

.sticky-menu {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 2;
    margin: 0 auto;
    border-radius: 0 0 20px 20px;
    transition: all .5s ease-in-out;
    padding: 1rem;

    &__shadow {
        background: #ffffff;
        box-shadow: 0 29px 28px #00000040;
    }
}

.container {
    height: 100%;
}