export enum siteEnable {
  true = 1,
  false = 0
}

export interface RecomendedData{
  success: boolean;
  data: Recomended[];
}

export interface Recomended{
  id: number;
  cat_id: number;
  name: string;
  description: string;
  img: string;
  hidden?: boolean;
  price: Price;
}

export interface Price{
  id: number;
  price: number;
  weight: string;
  img: string;
  pieces: number;
}

export interface PreorderTime {
  value: string;
  text: string;
}

export interface PreorderTimesInterface {
  success: boolean;
  data: PreorderTime[];
}
