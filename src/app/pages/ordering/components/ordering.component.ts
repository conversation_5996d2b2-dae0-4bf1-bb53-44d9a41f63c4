import { After<PERSON>iewInit, ChangeDetector<PERSON>ef, Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { OrderingRestService } from "../services/ordering-rest.service";
import { EMPTY, Observable, catchError, debounceTime, filter, forkJoin, merge, of, pluck, switchMap, take, takeUntil, tap } from "rxjs";
import { ActivatedRoute, Router } from "@angular/router";
import { CartRestService } from "../../../shell/components/navbar/cart/services/cart-rest.service";
import { Bonuses, BonusesInterface } from "../../../shell/components/navbar/cart/interfaces/cart.interface";
import { Unsubscribe } from "../../../unsubscribe";
import { Products } from "../../../shell/components/navbar/cart/interfaces/cart.interface";
import { Addresses } from "../../profile/interfaces/profile.interface";
import { FooterRestService } from "../../../shell/components/footer/services/footer-rest.service";
import { StorageLogicService } from "../../../shared/utils/services/storage-logic.service";
import { GoogleAnalyticsLogicService } from "../../../shared/analytics/service/GoogleAnalytics-logic.service";
import { CartLogicService } from "../../../shell/components/navbar/cart/services/cart-logic.service";
import { PreorderTime, Recomended } from "../interfaces/ordering.interface";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';
import { ProfileDataservice } from '../../profile/services/profile-data.service';
import { City } from 'src/app/shell/components/footer/interfaces/footer.interface';
import { ProfileRestService } from '../../profile/services/profile-rest.service';
import { LocationApiService } from 'src/app/shared/utils/services/location-api.service';
import { DeliveryLocation, DeliveryLocationZoneCoverage, DeliveryZone } from 'src/app/shared/interfaces/delivery-location.interface';
import { ToastService } from 'src/app/shared/utils/services/toast.service';
import { isAddressValid } from '../validators/is-route.validator';
import { CityDynamicSettingsService } from 'src/app/shared/cityDynamicSettings/city-dynamic-settings.service';
import { CityDynamicSettingsDTO } from 'src/app/shared/cityDynamicSettings/city-dynamic-settings.model';

@Component({
  selector: 'app-module',
  templateUrl: './ordering.component.html',
  styleUrls: ['./ordering.component.scss'],
})
export class OrderingComponent extends Unsubscribe implements OnInit, AfterViewInit, OnDestroy {
  preorderTimes!: Observable<PreorderTime[]>;
  preorderTime!: string;
  wantPreOrder = false;
  wantPayByBonus = false;
  dontCall = false;
  house = false;
  birthdayStatus = false;
  address: Addresses | undefined | null;
  phoneLength: number = 8
  liqPayData = '';
  liqPaySignature = '';
  places: City[] = [];
  future_datetime = '';
  form!: FormGroup;

  recommended: Recomended[] = [];
  displayedCards: Recomended[] = [];
  currentIndex: number = 0;
  cardsAnimationState: number = 0;
  productId: number = 0;
  firstFourCards!: Recomended[];
  protected readonly Number = Number;
  @ViewChild('cardProduct') cardProduct!: Recomended[];
  @ViewChild('recommendedProduct', { static: true }) recommendedProduct: TemplateRef<Recomended[]> | undefined;
  @ViewChild('blacklistedPhone', { static: true }) blacklistedPhoneModalRef!: TemplateRef<any>
  @ViewChild('invalidAddressRef') addressIsRouteErrorRef!: TemplateRef<any>;

  public giftProductsForReq: number[] = [];

  public birthdayInputValue = false;

  public locationDropdownOptions = signal<DeliveryLocation[] | null>(null);

  public selectedDeliveryLocation = signal<DeliveryLocation | null>(null);

  private readonly locationApiService = inject(LocationApiService);

  private readonly toastService = inject(ToastService);

  private readonly cdRef = inject(ChangeDetectorRef);

  private shouldPersistFormData = signal<boolean>(true);

  public mapId: string | null = null;

  public addressRefErrorText = signal<string | null>(null);

  public supportPhoneNumber: string | null = null;

  preorderDatepickerSettings = signal<{
    disabledDates: string[],
    daysAhead: number,
    enabled:boolean
  }>({ disabledDates: [], daysAhead: 0, enabled: true });
  
  preorderFlag = new FormControl(false);

  constructor(
    private GAService: GoogleAnalyticsLogicService,
    public cartRestService: CartRestService,
    public cartLogicService: CartLogicService,
    private service: OrderingRestService,
    private router: Router,
    private footerService: FooterRestService,
    private fb: FormBuilder,
    public storageService: StorageLogicService,
    private modalService: NgbModal,
    private route: ActivatedRoute,
    public countCityService: CountryCityNotificationService,
    public profileData: ProfileDataservice,
    protected countryCityService: CountryCityNotificationService,
    private readonly profileRestService: ProfileRestService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly cityDynamicSettingsService: CityDynamicSettingsService,
  ) {
    super();

    this.initBirthdayData();
  }

  ngOnInit(): void {
    this.getPreorderSettings();
    this.initGoogleMapId();
    this.initForm();
    this.preorderTimes = this.getPreorderTime(0);
    this.cartLogicService.viewCart = false;
    this.setValidation();
    this.isHouse();
    this.getPlaces().subscribe();
    this.getRecommended().subscribe();
    this.changeDeliveryType();
    this.updatePlaces();
    this.updateValidatorsListener();
    this.observeAddressCtrl();
    // this.paymentTypeListener();
    this.patchForm();

    this.handlePreorderChanges();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();

    this.shouldPersistFormData() ? this.saveFormOnNavigatingAway() : this.storageService.removeSessionData('ordering-form');
  }

  private getPreorderSettings(): void {
    this.cityDynamicSettingsService.getCityDynamicSettings()
      .pipe(
        take(1),
        takeUntil(this.$destroy))
    .subscribe({
      next: ({ data: { city: { future_ordering} } }: CityDynamicSettingsDTO) => {
        const { enabled, disabled_dates, days_ahead } = future_ordering;
        this.preorderDatepickerSettings.set({
          disabledDates: disabled_dates || [],
          daysAhead: days_ahead || 0,
          enabled:enabled
        })
        this.preorderFlag.setValue(!enabled);
        if (!enabled) {
          this.preorderFlag.disable();
        }
      },
    })
  }

  private handlePreorderChanges(): void {

    this.preorderFlag.valueChanges
      .pipe(
        switchMap(val => {
          if (val) {
            return this.cartLogicService.updateBonusInfo(this.form.get('delivery_type')?.value === 'pickup' ? 1 : 0);
          }
          return of(val);
        }),
        takeUntil(this.$destroy)
    )
      .subscribe(() => {
        this.wantPreOrder = !this.wantPreOrder;
        this.future_datetime = '';
      });
  }

  initBirthdayData() {
    this.route.data.subscribe(({ isUserBirthday }) => {
      this.birthdayStatus = isUserBirthday;
      this.birthdayInputValue = isUserBirthday
    });
  }

  updatePlaces() {
    merge(
      this.countCityService.languageChanged$,
      this.countCityService.cityChanged$
    ).pipe(
      takeUntil(this.$destroy),
      switchMap(() => forkJoin([this.footerService.getPlaces(), this.getRecommended()]))
    ).subscribe();
  }

  initForm(): void {
    this.form = this.fb.group(({
      delivery_type: ['delivery', [Validators.required]],
      place_id: [null, [Validators.required]],
      phone: [null, [Validators.required,
      Validators.maxLength(this.countryCityService.selectedCountry.phone_code.length + this.phoneLength),
      Validators.minLength(this.countryCityService.selectedCountry.phone_code.length + this.phoneLength)
      ]],
      address: this.fb.group({
        address_name: [null],
        address_id: [null],
        coordinates: [null],
        city: [null],
        is_route: [null],
        is_verified: [false],
        is_available: [false],
        zone_data: [null],
      }),
      clientName: [null],
      apt: [null, [Validators.required]],
      comment: [''],
      payment_type: ['cash', [Validators.required]],
      cart_id: [this.storageService.getData('cart'), [Validators.required]],
      city_id: [this.storageService.getData('city'), [Validators.required]],
      attic: [null],
      floor: [null],
      app: ['site', [Validators.required]],
      stick_edu: [null],
      stick: [null],
      birthday: [this.birthdayInputValue, [Validators.required]],
      dont_call: [false, [Validators.required]],
      isHouse: [false],
      bonuses: [0, [Validators.min(0), Validators.max(Number(this.profileData.bonusesBalance?.balance || 0))]],
      preorder_date: [null]
    }), { validators: [isAddressValid()] });
  }

  returnBonuses(): Bonuses {
    return this.cartLogicService.cartProducts.bonus;
  }

  get minPrice() {
    return this.form.get('delivery_type')?.value === 'pickup' ? 0 : this.countCityService.cityInfo.min_price;
  }

  get isHouseCtrlValue(): boolean {
    return this.form.get('isHouse')?.value;
  }

  isHouse(): void {
    this.form.get('isHouse')?.valueChanges.pipe(
      takeUntil(this.$destroy)
    ).subscribe(isHouse => {
      const isDontCall = this.form.get('dont_call')?.value;
      const fields = ['floor', 'attic', 'apt'] as const;

      const validator = !isHouse && isDontCall ? [Validators.required] : [];
      const aptValidator = !isHouse ? [Validators.required] : [];

      fields.forEach(field => {
        this.form.get(field)?.setValidators(field === 'apt' ? aptValidator : validator);
        this.form.get(field)?.updateValueAndValidity();
      });
    }
    )
  }

  preorderSetWithNoTimeSelected() {
    return this.wantPreOrder && !this.future_datetime;
  }

  setAddress(address: Addresses | null): void {
    if (address?.id === this.address?.id) {
      return;
    }
    this.address = address;
    if (this.address !== null) {
      if (!this.address.latitude && !this.address.longitude) {
        this.handleUnverifiedAddress(address!);
      } else {
        this.handleVerifiedAddress(address!);
      }
      this.form.patchValue({
        attic: this.address.attic,
        floor: this.address.floor,
        apt: this.address.apt
      });

    } else {
      const val = '';
      this.form.patchValue({
        attic: val, floor: val, apt: val
      });

      this.form.get('address')?.reset();
    }
  }

  handleUnverifiedAddress(primaryAddress: Addresses) {
    this.locationApiService.getUnverifiedAddressCoordinates(primaryAddress.id).subscribe(res => {
      if (res.success) {
        this.addressCoordinates?.setValue(res.data?.coordinates);
        this.addressDeliveryAvailable?.setValue(res.data?.available);
        this.addressId?.setValue(res.data?.place_id);
        this.addressZoneData?.setValue(res.data?.zone || null);
        this.addressIsVerified?.setValue(null);
        /* Prevent reset in address name changes observer. */
        this.addressName?.setValue(res.data?.address, { emitEvent: false });
        this.addressName?.markAsDirty();
        this.form.patchValue({
          phone: this.profileData.Authorization?.phone.replace('+', ''),
          attic: primaryAddress.attic ? primaryAddress.attic : '',
          floor: primaryAddress.floor ? primaryAddress.floor : '',
          apt: primaryAddress.apt ? primaryAddress.apt : ''
        });
        this.address = { ...primaryAddress, latitude: res.data?.coordinates?.lat || null, longitude: res.data?.coordinates.lng || null };
      }
    })
  }

  fillPrimaryAddress() {
    const primaryAddress = this.profileData.addresses?.primary;

    if (primaryAddress) {
      if (!primaryAddress?.longitude && !primaryAddress?.latitude) {
        this.handleUnverifiedAddress(primaryAddress);
        return;
      }
      this.address = primaryAddress;
      this.form.patchValue({
        phone: this.profileData.Authorization?.phone.replace('+', ''),
        attic: primaryAddress.attic ? primaryAddress.attic : '',
        floor: primaryAddress.floor ? primaryAddress.floor : '',
        apt: primaryAddress.apt ? primaryAddress.apt : ''
      });
      this.handleVerifiedAddress(primaryAddress);
    }
  }

  handleVerifiedAddress(address:Addresses) {
    const coordinates = { lat: Number(address.latitude), lng: Number(address.longitude) };
    this.addressCoordinates?.setValue(coordinates);
    this.addressIsVerified?.setValue(true);
    this.addressName?.setValue(address.address, { emitEvent: false });

    /** fetch zone data for validation */
    this.locationApiService.checkDeliveryCoverageByCoordinates(coordinates).pipe(
      take(1),
    ).subscribe(res => {
      if (res.data) {
        const { available, zone } = res.data;
        this.addressZoneData?.setValue(zone);
        this.addressDeliveryAvailable?.setValue(available)
      }
    })
  }

  changeDeliveryType(): void {
    let bonusesObservable: Observable<BonusesInterface>;
    this.form.get('delivery_type')?.valueChanges.pipe(
      takeUntil(this.$destroy),
      tap((deliveryType) => {
        const isDontCall = this.form.get('dont_call')?.value;
        const isPrivateHouse = this.form.get('isHouse')?.value;
        this.future_datetime = '';
        if (deliveryType === 'pickup') {
          this.preorderTimes = this.getPreorderTime(1);
          this.cartLogicService.salePercentChanged.next(5);
          bonusesObservable = this.cartLogicService.updateBonusInfo(1, this.future_datetime ? this.future_datetime : undefined);
          this.form.get('clientName')?.setValidators([Validators.required, Validators.pattern("^[a-zA-Zа-яА-ЯїЇіІёЁ\']+$")]);
          this.removeValidator(['address', 'attic', 'floor', 'apt']);
          if (isDontCall) this.addRequiredValidator(['stick', 'stick_edu']);
          this.removeAddressValidators()
        } else {
          this.form.get('clientName')?.setValidators([]);
          this.preorderTimes = this.getPreorderTime(0);
          this.cartLogicService.salePercentChanged.next(0);
          bonusesObservable = this.cartLogicService.updateBonusInfo(0, this.future_datetime ? this.future_datetime : undefined);
          if (isPrivateHouse && isDontCall) {
            this.addRequiredValidator(['address', 'stick', 'stick_edu']);
            this.removeValidator(['attic', 'floor', 'apt']);

          } else if (isPrivateHouse && !isDontCall) {
            this.addRequiredValidator(['address']);
            this.removeValidator(['attic', 'floor', 'apt', 'stick', 'stick_edu']);

          } else if (!isPrivateHouse && isDontCall) {
            this.addRequiredValidator(['address', 'attic', 'floor', 'apt', 'stick', 'stick_edu']);

          } else {
            this.addRequiredValidator(['address', 'apt']);
            this.removeValidator(['attic', 'floor', 'stick', 'stick_edu']);
          }
          this.addAddressValidators()
        }
        ['address', 'attic', 'floor', 'apt', 'stick', 'stick_edu', 'clientName']
          .forEach(elem => this.form.get(elem)?.updateValueAndValidity());
      }),
      switchMap(() => bonusesObservable)
    ).subscribe();
  }

  private addAddressValidators() {
    this.form.addValidators([isAddressValid])
  }

  private removeAddressValidators() {
    this.form?.removeValidators([isAddressValid])
  }

  addRequiredValidator(validate: Array<string>) {
    validate.forEach(element => {
      this.form.get(element)?.setValidators(Validators.required);
    });
  }

  removeValidator(invalidate: Array<string>) {
    invalidate.forEach(element => {
      this.form.get(element)?.setValidators([]);
    })
  }

  updateValidatorsListener() {
    this.countCityService.countryChangeFinished$
      .pipe(
        takeUntil(this.$destroy),
        tap(() => {
          this.form.get('phone')?.setValidators([Validators.required,
          Validators.maxLength(this.countCityService.selectedCountry.phone_length + this.countCityService.selectedCountry.phone_code.length - 1),
          Validators.minLength(this.countCityService.selectedCountry.phone_length + this.countCityService.selectedCountry.phone_code.length - 1)
          ]);
          this.form.get('phone')?.updateValueAndValidity();
        })).subscribe();
  }

  getPlaces() {
    return this.footerService.getPlaces()
      .pipe(tap((value) => {
        this.places = value.data.city;
        this.form.get('place_id')?.setValue(this.places[0].id);
      }));
  }

  sendOrder(): void {
    const items = this.cartLogicService.cartProducts.products.map((product: Products) => ({
      item_name: product.name,
      item_id: product.id,
      price: product.price,
      quantity: product.quantity
    }));
    this.GAService.eventEmitter('begin_checkout', { items });

    if (this.form.get('delivery_type')?.value === 'delivery') {
      this.form.get('place_id')?.setValue(null);
    } else {
      this.form.get('address')?.reset();
      this.address = null;
      this.form.get('apt')?.setValue(null)
      this.form.get('attic')?.setValue(null)
      this.form.get('floor')?.setValue(null)
      const clientName = this.form.get('clientName')?.value;
      const comment = this.form.get('comment')?.value;

      this.form.get('comment')?.setValue(clientName ? `${clientName}/${comment}` : comment);
    }

    if (this.birthdayStatus) {
      this.form.value.birthday = 1;
    } else {
      this.form.value.birthday = 0;
    }
    if (this.dontCall) {
      this.form.value.dont_call = 1;
    } else {
      this.form.value.dont_call = 0;
    }



    const formattedFormData = this.formatFormDataForSubmission();

    this.shouldPersistFormData.set(false);


    if (this.cartLogicService.cartTotal <= 59 && this.form.get('delivery_type')?.value === 'pickup') {
      this.giftProductsForReq = [];
    }

    this.service.sendOrder(
      this.future_datetime === '' ?
        { ...formattedFormData, displayed_bonuses: this.returnBonuses()?.bonus_value, gifts: this.giftProductsForReq } :
        { ...formattedFormData, ...this.addDateTimeToOrderRequest(), displayed_bonuses: this.returnBonuses()?.bonus_value, gifts: this.giftProductsForReq },
    )
      .pipe(
        catchError(e => {
          if (e.status && e.status === 403) {
            this.supportPhoneNumber = `+38 ${this.storageService.getData('cityInfo')?.operators[0]?.phone}`
            this.openBlacklistModal();
          }
          return EMPTY;
        }),
        tap(() => {
          if (this.form.get('payment_type')?.value !== 'online') {
            this.router.navigate(['order-success'], { relativeTo: this.activatedRoute, queryParams: { ...(!this.preorderDatepickerSettings().enabled && { preorder: true }) } });
          } else {
            this.router.navigate(['/','checkout-card'], {
              queryParams: {
                cart_id: this.storageService.getData('cart')
              }
            }).then();
          }
        })
      )
      .subscribe();
  }

  private addDateTimeToOrderRequest() {
    const date = this.form.get('preorder_date')?.value;

    const padWithZero = (date: number) => {
      return date < 10 ? `0${date}` : date;
    }

    const formattedDate = `${date.year}-${padWithZero(date.month)}-${padWithZero(date.day)}`;

    return {
      future_ordering_datetime: `${formattedDate} ${this.future_datetime}`
    }
  }

  private formatFormDataForSubmission() {
    const { address, ...formData } = this.form.value;

    const formattedAddress = {
      address_id: this.address?.id || null,
      address: address.address_name?.trim() || null,
      lat: address.coordinates?.lat || null,
      lng: address.coordinates?.lng || null,
    }

    const formattedFormData = { ...formData, ...formattedAddress };
    return formattedFormData;
  }

  setValidation() {
    this.form.get('dont_call')?.valueChanges
      .pipe(
        takeUntil(this.$destroy)).subscribe(isDontCall => {
          const isHouse = this.form.get('isHouse')?.value;
          const deliveryType = this.form.get('delivery_type')?.value;

          if (isHouse && isDontCall) {
            this.addRequiredValidator(['stick', 'stick_edu']);
            this.removeValidator(['attic', 'floor', 'apt']);
            if (deliveryType === 'pickup') {
              this.removeValidator(['address']);
            }
          } else if (!isHouse && !isDontCall) {
            this.form.get('apt')?.setValidators(Validators.required);
            this.removeValidator(['attic', 'floor', 'stick', 'stick_edu']);
            if (deliveryType === 'pickup') {
              this.removeValidator(['address', 'attic', 'floor', 'apt']);
            }
          } else if (isHouse && !isDontCall) {
            this.removeValidator(['attic', 'floor', 'apt', 'stick', 'stick_edu']);
            if (deliveryType === 'pickup') {
              this.removeValidator(['address']);
            }
          } else {
            this.addRequiredValidator(['attic', 'floor', 'apt', 'stick', 'stick_edu']);
            if (deliveryType === 'pickup') {
              this.removeValidator(['address', 'attic', 'floor', 'apt']);
            }
          }

          ['address', 'attic', 'floor', 'apt', 'stick', 'stick_edu']
            .forEach(elem => this.form.get(elem)?.updateValueAndValidity());
        }
        );
  }

  handleDeleteProduct(productId: number) {
    this.cartLogicService.deleteProduct(productId);
  }

  handleAddProduct(productId: number) {
    this.cartLogicService.addProduct(productId, false);
  }

  handleDeleteAllProduct(id: number) {
    this.cartLogicService.deleteAllOfSameProduct(id);
  }

  calcTotalPriceFreeProduct(id: number) {
    const prod = this.cartLogicService.freeProductsData.find(product => product.id === id)!;
    return prod.quantity * (prod.paid_product?.price || prod.gift_product?.price!);
  }

  onInputFocus() {
    const phoneControl = this.form.get('phone');
    if (phoneControl) {
      const currentValue = phoneControl.value || '';
      if (currentValue.trim() === '' && !currentValue.startsWith('+38')) {
          phoneControl.setValue('+38' + currentValue);
      }
    }
  }

  get checkQuantityBonuses(): boolean {
    if (!this.profileData.bonusesBalance) return true;
    return this.form.get('bonuses')?.value > +this.profileData.bonusesBalance?.balance;
  }

  getPreorderTime(pickup: number): Observable<PreorderTime[]> {
    return this.service.getPreorderTime(pickup).pipe(pluck('data'));
  }

  setPreorderTime(time: { value: string, text: string }): void {
    this.cartLogicService.updateBonusInfo(this.form.get('delivery_type')?.value === 'pickup' ? 1 : 0, time.value).subscribe();
    this.preorderTime = time.text;
    this.future_datetime = time.value;
  }

  buttonDisabled() {
    const isDelivery = this.form.get('delivery_type')?.value === 'delivery';
    const isTotalInsufficient = isDelivery && this.cartLogicService.cartTotal < (this.addressZoneData?.value?.min_price || 0);
    const deliveryZoneConflict = isDelivery && !this.addressDeliveryAvailable?.value;
    return this.form.invalid || this.preorderSetWithNoTimeSelected() || isTotalInsufficient || deliveryZoneConflict;
  }

  changeFlagBuyByBonuses(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    const checked = checkbox.checked;
    const bonusesField = this.form.get('bonuses')

    checked ? bonusesField?.setValidators([Validators.required]) : bonusesField?.setValidators([])
    bonusesField?.setValue('');
    this.wantPayByBonus = !this.wantPayByBonus;
  }

  getRecommended() {
    return this.service.getRecommendedProducts().pipe(
      tap((recommendedData) => {
        this.recommended = recommendedData.data;
        this.firstFourCards = this.recommended.slice(0, 4);
        this.updateDisplayedCards(this.currentIndex);
        if (window.innerWidth <= 768 && this.recommended.length >= 4) {
          this.openModalWithRecommendedProducts();
        }
      })
    );
  }

  updateDisplayedCards = (currIndex: number): void => {
    const arrayLength = this.recommended.length;
    const threeCards = 3;
    this.displayedCards = [];
    for (let i = 0; i < threeCards; i++) {
      const index = (currIndex + i) % arrayLength;
      if (i < arrayLength) {
        this.displayedCards.push(this.recommended[index]);
      }
    }
  }

  openModal(productId: number): void {
    this.GAService.eventEmitter(
      'view_item',
      {
        items: [{
          item_name: this.recommended.find((item) => item.id === productId)?.name,
          item_id: this.recommended.find((item) => item.id === productId)?.id,
          price: this.recommended.find((item) => item.id === productId)?.price.price,
          quantity: 1
        }]
      });
    this.router.navigate(['ordering'], {
      queryParams: {
        productId: productId
      }
    });
  }

  ngAfterViewInit(): void {
    this.subscribeOnQueryParamsChange();
  }

  subscribeOnQueryParamsChange(): void {
    this.route.queryParams.pipe(
      takeUntil(this.$destroy)
    ).subscribe({
      next: (value: any) => {
        this.productId = value?.productId
        if (value?.productId) {
          this.modalService.open(this.cardProduct, {
            size: 'xl'
          }).result.finally(() => {
            this.closeModal();
          });
        }
      }
    });
  }

  closeModal(): void {
    this.storageService.removeData('product_modal_id');
    this.router.navigate(['ordering']).then(() => this.modalService.dismissAll());
  }

  openModalWithRecommendedProducts(): void {
    const isMobile = window.innerWidth <= 768;
    if (isMobile) {
      this.modalService.open(this.recommendedProduct);
    }
  }

  handleGiftProductCheck(event: Event, id: number) {
    const isChecked = (event.target as HTMLInputElement).checked;
    if (isChecked) {
      this.giftProductsForReq = [...this.giftProductsForReq, id]
    }
    else {
      this.giftProductsForReq = this.giftProductsForReq.filter(giftId => giftId !== id)
    }
  }

  trackCartItem(index: number, item: Products) {
    return item.id
  }

  openBlacklistModal() {
    this.modalService.open(this.blacklistedPhoneModalRef);
  }

  get addressIsVerified(): FormControl | null {
    return this.form.get('address')?.get('is_verified') as FormControl | null;
  }

  get addressName(): FormControl | null {
    return this.form.get('address')?.get('address_name') as FormControl | null;
  }

  get addressId(): FormControl | null {
    return this.form.get('address')?.get('address_id') as FormControl | null;
  }

  get addressCoordinates(): FormControl | null {
    return this.form.get('address')?.get('coordinates') as FormControl | null;
  }

  get addressDeliveryAvailable(): FormControl | null {
    return this.form.get('address')?.get('is_available') as FormControl | null;
  }

  get addressZoneData(): FormControl<DeliveryZone | null> | null {
    return this.form.get('address')?.get('zone_data') as FormControl<DeliveryZone | null> | null;
  }

  get addressIsRoute(): FormControl | null {
    return this.form.get('address')?.get('is_route') as FormControl | null;
  }


  private observeAddressCtrl(): void {
    if (this.addressName) {
      this.addressName.valueChanges.pipe(
        tap(val => {
          if (!val?.trim()?.length) {
            this.locationDropdownOptions.set(null);
          }
          if (this.addressName?.dirty) {
            this.addressId?.reset();
            this.addressCoordinates?.reset();
            this.addressIsVerified?.reset();
          }
        }),
        filter(val => val?.length > 2),
        debounceTime(1500),
        switchMap(val => this.locationApiService.getLocationAutocompleteOptions(val)),
        tap((response) => {
          this.address = null;
          if (response.success)
            this.locationDropdownOptions.set(response.data?.predictions || []);
        }),
      ).subscribe();
    }
  }

  private updateDeliveryLocationWithZone(place_id: string | null, updateVerificationStatus = false): void {
    if (!place_id) {
      return;
    }
    this.locationApiService.checkDeliveryCoverageByPlaceId(place_id)
      .pipe(
        take(1),
      )
      .subscribe(
        val => {
          if (!val.data?.address) {
            this.handleFalsyAddress();
            return;
          }
          this.patchAddressWithConfirmedLocation(val.data, updateVerificationStatus);
          if (this.address) {
            this.address = { ...this.address, address: this.addressName?.value };
          }
        }
      )
  }

  private handleFalsyAddress() {
    this.addressRefErrorText.set("ZONE_COVERAGE.ENTER_VALID_ADDRESS");
    this.showLocationErrorMessage();
  }

  private patchAddressWithConfirmedLocation(location?: DeliveryLocationZoneCoverage, updateVerifiedStatus = false) {
    if (!location) {
      return;
    }
    const addressName = !location.is_address_incomplete && {address_name: location.address};
    const patchedLocation = { address_id: location.place_id, ...addressName, coordinates: location.coordinates, city: location.city, is_zone: null, is_verified: updateVerifiedStatus, is_available: location.available, is_route: location.is_route, zone_data: location.zone }

    this.form.patchValue({ address: patchedLocation }, { emitEvent: false });
    this.form.updateValueAndValidity();
  }

  public selectLocation(location: DeliveryLocation, isModal = false, content?: TemplateRef<any>): void {
    this.patchAddressWithSelectedLocation(location);



    if (location?.is_route && !this.form.get('isHouse')?.value) {
      this.addressRefErrorText.set("ZONE_COVERAGE.ENTER_HOUSE_NUMBER");
      this.showLocationErrorMessage();
    }
    this.updateDeliveryLocationWithZone(location.id);
    if (isModal && content) {
      this.openDeliveryConfirmationModal(content);
    }
  }

  private patchAddressWithSelectedLocation(location: DeliveryLocation | DeliveryLocationZoneCoverage): void {
    let patchedLocation = {};
    if ('id' in location) {
      patchedLocation = { address_id: location.id, address_name: location.address, city: location.city, is_route: location.is_route }
    }
    if ('place_id' in location) {
      const addressName = !location.is_address_incomplete && { address_name: location.address };
      patchedLocation = { address_id: location.place_id, ...addressName, city: location.city, is_route: location.is_route }
    }
    this.form?.patchValue({ address: patchedLocation }, { emitEvent: false });
  }

  private showLocationErrorMessage(): void {
    this.toastService.show({
      header: '',
      body: '',
      template: this.addressIsRouteErrorRef,
      classname: 'bg-danger text-light',
      autohide: false
    });
  }

  public confirmDeliveryAddress(isDesk = false): void {
    //Closing the confirmation modal for mobile devices
    if (!isDesk) {
      this.modalService.dismissAll();
    }
    const updateVerificationStatue = true;
    this.updateDeliveryLocationWithZone(this.addressId?.value, updateVerificationStatue);
  }

  public handleMarkerMove(event: google.maps.LatLngLiteral): void {
    this.locationApiService.checkDeliveryCoverageByCoordinates(event).pipe(
      take(1),
    ).subscribe(res => {
      if (res.data) {
        this.patchAddressWithSelectedLocation(res.data);
      }
    })
  }

  public openDeliveryConfirmationModal(content: TemplateRef<any>): void {
    this.modalService.open(content, {
      size: 'xl',
      centered: true,
      keyboard: false,
    })
  }

  private saveFormOnNavigatingAway(): void {
    this.storageService.setSessionData('ordering-form', this.form.value);
  }

  private patchForm(): void {
    try {
      const formDataFromStorage = this.storageService.getSessionData('ordering-form');
      if (formDataFromStorage) {
        this.form.patchValue(formDataFromStorage, { emitEvent: true });
        this.addressName?.markAsDirty();
        this.form.updateValueAndValidity();
        this.cdRef.detectChanges();
      } else {
        this.fillPrimaryAddress();
      }
      this.form.get('cart_id')?.setValue(this.storageService.getData('cart'));
    } catch (e) {
      console.log('error', e)
    }
  }

  private initGoogleMapId(): void {
    this.mapId = this.storageService.getData('cityInfo').google_maps_map_id;
  }

  get isBonusProductAvailable(): boolean {
    return this.cartLogicService.cartTotal <= 250;
  }
}
