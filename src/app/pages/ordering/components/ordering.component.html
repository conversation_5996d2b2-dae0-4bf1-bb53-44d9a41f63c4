<div class="container">
  <h1 class="d-none" translate="ORDERING.ORDER_PLACEMENT"></h1>
  <div *ngIf="returnBonuses() && profileData.Authorization && countCityService.cityModule?.bonuses?.status === 1" class="d-flex w-100 justify-content-center align-items-center mb-4">
    <div class="bonus d-flex justify-content-between align-items-center" #bonus>
      <div class="d-flex w-100">
        <div class="d-flex align-items-center justify-content-center flex-column w-25">
          <span class="bonus__title" translate="BONUSES.BONUSES"><span>:</span></span>
          <span class="bonus__quantity">{{(returnBonuses().bonus_value || 0).toFixed(2)}}{{countCityService.selectedCountry.currency}}</span>
        </div>
        <div class="d-flex w-75 flex-column mx-4">
          <div>
            <ngb-progressbar style="height: 5px" class="w-100" type="secondary" [max]="cartLogicService.maxBonus" [value]="cartLogicService.activeBonus"></ngb-progressbar>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <span class="me-2">0%</span>
            <span class="green-font-order fw-bold fs-16">{{cartLogicService.activeBonus}}%</span>
            <span class="ms-2">{{cartLogicService.maxBonus}}%</span>
          </div>
          <button routerLink="../info" [queryParams]="{page: 'bonus'}" class="bonus-button none-border" translate="BONUSES.WANT_MORE"></button>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <h2 class="d-none" translate="ORDERING.ORDER_DETAILS"></h2>
    <div class="col-2 free-product-mobile">
      <div class="ordering">

        <ng-container *ngFor="let object of cartLogicService.cartProducts.free_products">
          <div class="free-product-block"
            [class.gift-product__grayscale]="object.gift_product&& !giftProductsForReq.includes(object.gift_product.gift_id)">
            <div class="free-product" dimensions>
              <img class="free-product__image" *ngIf="object.img" [attr.data-src]="object.img" alt="Product image"
                loading="lazy" appImgStyle>
              <div>
                <div class="free-product__name">{{object.name}}</div>
                <span class="free-product__per-piece"
                  *ngIf="object.paid_product; else giftPerPiece">{{object?.paid_product?.price| number: '1.0-2'}}
                  {{countCityService.selectedCountry.currency}} <span translate="ORDERING.PER_PIECE"></span></span>
                <ng-template #giftPerPiece>
                  <span class="free-product__per-piece">{{object.quantity}}<span>шт</span> {{object.weight}}</span>
                </ng-template>
              </div>
            </div>

            <div class="quantity-price" *ngIf="object.paid_product; else giftOrder">
              <div>
                <span class="fw-bold me-2 align-middle cursor-pointer"
                  (click)="handleDeleteProduct(object?.paid_product?.id!)">-</span>
                <span class="green-font-order align-middle">{{object.quantity}}</span>
                <span class="fw-bold ms-2 align-middle cursor-pointer" (click)="handleAddProduct(object?.paid_product?.id!)">+</span>
              </div>
              <span class="quantity-price__total fw-bold"
                *ngIf="object.quantity * object.paid_product.price! - calcTotalPriceFreeProduct(object.id) !== 0">{{object.quantity
                * object.paid_product.price - calcTotalPriceFreeProduct(object.id) | number: '1.0-2'}}
                {{countCityService.selectedCountry.currency}}</span>
              <span class="quantity-price__free"
                *ngIf="object.quantity * object.paid_product.price! - calcTotalPriceFreeProduct(object.id) === 0"
                translate="ORDERING.FREE"></span>
            </div>

            <ng-template #giftOrder>
              <div class="gift-product__order">
                <div class="gift-product__checkbox-wrapper">
                  <input #giftProductCheckbox type="checkbox" [disabled]="isBonusProductAvailable"
                    (change)="handleGiftProductCheck($event, object.gift_product!.gift_id)"
                    [checked]="giftProductsForReq.includes(object.gift_product?.gift_id!) && !isBonusProductAvailable"
                         [ngbTooltip]="'ORDERING.TO_SELECT_A_BONUS_PRODUCT_PLEASE_ADD_THE_MAIN_PRODUCT' | translate"
                         [disableTooltip]="!isBonusProductAvailable">
                </div>
                <span class="quantity-price__free" translate="ORDERING.FREE"></span>
              </div>
            </ng-template>
          </div>

        </ng-container>
      </div>
    </div>
    <div class="col-5">
      <div class="ordering">
        <ng-container *ngFor="let object of cartLogicService.cartProducts.products; trackBy: trackCartItem">
          <app-ordering-product-card [product]="object" (addProduct)="handleAddProduct($event)" (deleteProduct)="handleDeleteProduct($event)" (deleteAllProduct)="handleDeleteAllProduct($event)"></app-ordering-product-card>
          <div *ngIf="cartLogicService.cartProducts.products[cartLogicService.cartProducts.products.length - 1] !== object"
               class="hr"></div>
        </ng-container>
        <div class="total">
          <div class="mx-2 d-flex justify-content-between flex-nowrap gap-1 align-items-center p-2">
            <div class="white-font font-size-12 text-nowrap">
              <img width="34" class="me-2" src="../../../assets/pay.webp" alt="Pay image" loading="lazy">
              <span class="white-font font-size-12 text-nowrap" translate="ORDERING.PAID"></span>
             :</div>
            <span *ngIf="cartLogicService.salePercent > 0 && form.get('delivery_type')?.value === 'pickup'"
                  class="font-size-12 white-font mobile-massage text-center">
              <span class="white-font font-size-12" translate="ORDERING.ACTION_DISCOUNT"></span> {{cartLogicService.saleInCurrency | number: '1.0-2'}} {{countCityService.selectedCountry.currency}}</span>
            <p class="white-font fw-bold text-nowrap mb-0">
              <span class="white-font">{{(cartLogicService.cartTotal).toFixed(2) | number: '1.0-2'}} </span>
              <span class="white-font fs-14">{{countCityService.selectedCountry.currency}}</span>
            </p>
          </div>
        </div>

        <div class="d-flex justify-content-center align-items-center my-5" *ngIf="!cartLogicService.cartProducts">
          <div class="lds-spinner">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>
      </div>
      <app-recommended-products
        [recommended]="recommended"
        (updateDisplayedCards)="updateDisplayedCards($event)"
        [country]="countCityService.selectedCountry"
        [displayedCards]="displayedCards"
        [currentIndex]="currentIndex"
        (openModal)="openModal($event)">
      </app-recommended-products>
    </div>
    <div class="col-5">
      <div class="ordering">
        <div class="container">
          <div class="row">
            <form [formGroup]="form">
              <div class="col-12 my-3">
                <span class="fw-bold font-size-12" translate="ORDERING.DELIVERY_METHOD"></span>
              </div>
              <div class="col-12 mb-2">
                <input class="hover" *ngIf="countCityService.cityModule?.delivery?.status" formControlName="delivery_type" type="radio"
                       value="delivery" name="delivery_type"
                       id="flexRadioDefault2">
                <label *ngIf="countCityService.cityModule?.delivery?.status" class="mt-1 ms-2" for="flexRadioDefault2">
                  <span translate="ORDERING.BY_ADDRESS"></span>
                </label>
                <input *ngIf="countCityService.cityModule?.pickup?.status" formControlName="delivery_type" class="ms-3 hover"
                       type="radio" value="pickup" name="delivery_type"
                       id="flexRadioDefault3">
                <label *ngIf="countCityService.cityModule?.pickup?.status" class="mt-1 ms-2" for="flexRadioDefault3">
                  <span translate="ORDERING.SELF_PICKUP"></span>
                </label>
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'pickup'" class="col-12 my-3">
                <span class="fw-bold font-size-12" translate="ORDERING.DEPARTMENT"></span>
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'pickup'" class="col-12 mb-2">
                <ng-container *ngFor="let object of places">
                  <input class="hover" formControlName="place_id" type="radio" [value]="object.id" name="place_id"
                         id="flexRadioDefault4">
                  <label class="mt-1 ms-2 me-2" for="flexRadioDefault4">
                    <span>{{object.address}}</span>
                  </label>
                </ng-container>
              </div>
              <div *ngIf="countCityService.cityModule?.dont_call?.status === 1 && profileData.Authorization?.phone" class="col-12 mt-3">
                <input formControlName="dont_call" type="checkbox" id="notCollBack" (change)="dontCall = !dontCall">
                <label class="ms-2 small-gray-font" for="notCollBack" translate="ORDERING.DONT_CALL"></label>
              </div>
              <div class="col-12 mt-3 mb-2">
                <span class="fw-bold font-size-12" translate="ORDERING.PHONE"></span>
              </div>
              <div class="col-12 mb-1">
                <input formControlName="phone" mask="{{countCityService.mask}}"
                       class="search w-100 hover" type="text"
                       placeholder="{{countCityService.placeholder}}" (focus)="onInputFocus()"
                >
              </div>
              <div *ngIf="form?.get('delivery_type')?.value === 'pickup'" class="col-12 mt-3 mb-2">
                <span class="fw-bold font-size-12" translate="ORDERING.YOUR_NAME"></span>
              </div>
              <div *ngIf="form?.get('delivery_type')?.value === 'pickup'" class="col-12 mb-1">
                <input formControlName="clientName" class="search w-100 hover" type="text">
              </div>

              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 my-3">
                <input formControlName="isHouse" type="checkbox" id="house">
                <label class="ms-2 small-gray-font" for="house" translate="ORDERING.HOUSE"></label>
              </div>

              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 ordering-desktop">
                <div class="row mb-2">
                  <div class="input-width-68">
                    <span class="fw-bold font-size-12" translate="LOCATION.STREET"></span>
                  </div>
                  <div *ngIf="!isHouseCtrlValue" class="input-width-28">
                    <span class="fw-bold font-size-12" translate="LOCATION.APARTMENT"></span>
                  </div>
                </div>
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 ordering-desktop position-relative">
                <ng-container formGroupName="address">
                  <input formControlName="address_name" class="search input-width-68 hover" type="text">
                </ng-container>
                    <!-- showing autocomplete -->
                   @if(addressName?.dirty && !addressId?.value && !addressIsVerified?.value){
                      <ng-container *ngTemplateOutlet="locationAutocompleteRef"></ng-container>
                   }
                   <!-- showing verification map -->
                   @if(addressName?.dirty && addressId?.value && !addressIsVerified?.value && addressCoordinates?.value){
                       <ng-container *ngTemplateOutlet="mapRef; context: { isDesk: true }"></ng-container>
                  }
                <input pattern="[0-9]+" *ngIf="!isHouseCtrlValue" formControlName="apt" class="search ms-2 input-width-28 hover" type="text">
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 ordering-mobile">
                <div class="row mb-2">
                  <div class="input-width-68">
                    <span class="fw-bold font-size-12" translate="LOCATION.STREET"></span>
                  </div>
                </div>
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 ordering-mobile position-relative">
                <ng-container formGroupName="address">
                  <input formControlName="address_name" class="search input-width-68 hover" type="text">
                </ng-container>
                @if(addressName?.dirty && !addressId?.value){
                  <ng-container *ngTemplateOutlet="locationAutocompleteRef; context: { isModal: true }"></ng-container>
                }
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 ordering-mobile">
                <div class="row mb-2">
                  <div *ngIf="!isHouseCtrlValue" class="input-width-68">
                    <span class="fw-bold font-size-12" translate="LOCATION.APARTMENT"></span>
                  </div>
                </div>
              </div>
              <div *ngIf="form.get('delivery_type')?.value === 'delivery'" class="col-12 mt-2 ordering-mobile">
                <input pattern="[0-9]+" *ngIf="!isHouseCtrlValue" formControlName="apt" class="search ms-1 input-width-68 hover" type="text">
              </div>
              <ng-container *ngIf="dontCall === true && form.get('delivery_type')?.value === 'delivery'">
                <div class="col-12 ordering-desktop">
                  <div *ngIf="!isHouseCtrlValue" class="row my-2">
                    <div class="input-width-48">
                      <span class="fw-bold font-size-12" translate="LOCATION.ENTRANCE"></span>
                    </div>
                    <div class="input-width-48">
                      <span class="fw-bold font-size-12" translate="LOCATION.FLOOR"></span>
                    </div>
                  </div>
                </div>
                <div *ngIf="!isHouseCtrlValue" class="col-12 ordering-desktop">
                  <input pattern="[0-9]+" formControlName="attic" class="search input-width-49 hover" type="text">
                  <input pattern="[0-9]+" formControlName="floor" class="search ms-2 input-width-49 hover" type="text">
                </div>
              </ng-container>
              <ng-container *ngIf="dontCall === true && form.get('delivery_type')?.value === 'delivery'">
                <div class="col-12 ordering-mobile">
                  <div *ngIf="!isHouseCtrlValue" class="row mt-2">
                    <div class="input-width-48">
                      <span class="fw-bold font-size-12" translate="LOCATION.ENTRANCE"></span>
                    </div>
                    <div class="input-width-48">
                      <span class="fw-bold font-size-12" translate="LOCATION.FLOOR"></span>
                    </div>
                  </div>
                </div>
                <div *ngIf="!isHouseCtrlValue" class="col-12 ordering-mobile">
                  <input pattern="[0-9]+" formControlName="attic" class="search input-width-49 hover" type="text">
                  <input pattern="[0-9]+" formControlName="floor" class="search ms-1 input-width-49 hover" type="text">
                </div>
              </ng-container>

              <div *ngIf="profileData.Authorization && form.get('delivery_type')?.value === 'delivery'" class="col-12 my-3">
                <span class="fw-bold font-size-12" translate="ORDERING.YOUR_ADDRESSES"></span>
              </div>
              <div *ngIf="profileData.Authorization && form.get('delivery_type')?.value === 'delivery'" class="col-12 mb-2">
                <div ngbDropdown class="d-inline-block nav-icon-mobile w-100">
                  @if(address?.address !== addressName?.value || !address){
                      <button class="search hover" ngbDropdownToggle translate="ORDERING.ANOTHER_ADDRESS"></button>
                  }
                  @else {
                    <button class="search hover" id="dropdownBasic3" ngbDropdownToggle>
                        <span *ngIf="address?.address" class="green-font-order">{{address.address}} </span>
                        <span *ngIf="address?.attic" class="green-font-order"><span translate="PROFILE.ENTRANCE_SMALL"></span>{{address.attic}}</span>
                        <span *ngIf="address?.floor" class="green-font-order"><span translate="PROFILE.FLOOR_SMALL"></span>{{address.floor}}</span>
                        <span *ngIf="address?.apt" class="green-font-order"> <span translate="PROFILE.APARTMENT_SMALL"></span>{{address.apt}}</span>
                    </button>
                  }

                  <div class="drop-menu-filter" ngbDropdownMenu aria-labelledby="dropdownBasic3">
                    <button class="drop-menu-button" ngbDropdownItem
                            (click)="setAddress(null)" translate="ORDERING.ANOTHER_ADDRESS"></button>
                    <button (click)="setAddress(object)" *ngFor="let object of profileData.addresses?.additional"
                            class="drop-menu-button" ngbDropdownItem>
                      {{address?.address?.includes(object.address)? address?.address:object.address}} {{object.house}} {{object.attic ? ('PROFILE.ENTRANCE_SMALL' | translate) : ''}} {{object.attic}} {{object.floor ? ('PROFILE.FLOOR_SMALL' | translate) : ''}} {{object.floor}}
                      {{object.apt ? ('PROFILE.APARTMENT_SMALL' | translate) : ''}} {{object.apt}}
                    </button>
                  </div>
                </div>
              </div>

              <div class="col-12 mt-3 mb-2">
                <span class="fw-bold font-size-12" translate="ORDERING.COMMENT"></span>
              </div>
              <div class="col-12 mb-1">
                <textarea formControlName="comment" class="search-textarea w-100 hover"></textarea>
              </div>


              <div *ngIf="countCityService.cityModule?.preorder?.status === 1" class="col-12 my-3">
                <input type="checkbox" id="preorderTime" [formControl]="preorderFlag">
                <label class="ms-2 small-gray-font" for="preorderTime" translate="ORDERING.PRE-ORDER"></label>
              </div>

              <div *ngIf="preorderFlag.value" class="col-12 mt-3 mb-2">
                <span class="fw-bold font-size-12" translate="ORDERING.MY-PREORDER"></span>
              </div>

              <div *ngIf="preorderFlag.value" class="row col-12 my-3 gx-2">
                <div ngbDropdown class="nav-icon-mobile col-12 col-md-6 mb-2 mb-md-0">
                  <app-datepicker [daysAhead]="preorderDatepickerSettings().daysAhead" [disabledDates]="preorderDatepickerSettings().disabledDates"/>
                </div>
                <div ngbDropdown class="nav-icon-mobile col-12 col-md-6">
                  <button *ngIf="future_datetime === ''" class="search hover" ngbDropdownToggle translate="ORDERING.PREORDER-TIME" [disabled]="(preorderTimes| async)?.length === 0"></button>

                  <button *ngIf="future_datetime !== ''" class="search hover" ngbDropdownToggle>
                    {{preorderTime}}</button>
                    <div *ngIf="(preorderTimes| async)?.length === 0" style="color: #c0304a; text-align: center;" class="mt-2 font-size-12" translate="ORDERING.NO_PREORDER_OPTIONS"></div>
                  <div class="drop-menu-filter" ngbDropdownMenu aria-labelledby="dropdownBasic5">
                    <button (click)="setPreorderTime(object)" *ngFor="let object of (preorderTimes| async)" class="drop-menu-button" ngbDropdownItem>
                      {{object.text}}
                    </button>
                  </div>
                </div>
              </div>
              <ng-container *ngIf="dontCall === true">
                <div class="col-12 ordering-desktop">
                  <span class="fw-bold font-size-12" translate="ORDERING.COUNT_APPLIANCES"></span>
                </div>
                <div class="col-12 my-3 ordering-desktop">
                  <input pattern="[0-9]+" formControlName="stick" class="search input-width-25" type="text"
                         placeholder="{{'ORDERING.STICK_NORMAL_PLACEHOLDER' | translate}}">
                  <input pattern="[0-9]+" formControlName="stick_edu" class="search ms-2 input-width-25" type="text"
                         placeholder="{{'ORDERING.STICK_EDU_PLACEHOLDER' | translate}}">
                </div>
              </ng-container>
              <ng-container *ngIf="dontCall === true">
                <div class="col-12 ordering-mobile">
                  <span class="fw-bold font-size-12" translate="ORDERING.COUNT_APPLIANCES"></span>
                </div>
                <div class="col-12 my-3 ordering-mobile">
                  <input pattern="[0-9]+" formControlName="stick" class="search input-width-25" type="text"
                         placeholder="{{'ORDERING.STICK_NORMAL_PLACEHOLDER' | translate}}">
                  <input pattern="[0-9]+" formControlName="stick_edu" class="search ms-1 input-width-25" type="text"
                         placeholder="{{'ORDERING.STICK_EDU_PLACEHOLDER' | translate}}">
                </div>
              </ng-container>
              <div *ngIf="birthdayInputValue" class="col-12 mt-2">
                <input formControlName="birthday" type="checkbox" id="birthday"
                  (change)="birthdayStatus = !birthdayStatus">
                <label class="ms-2 small-gray-font" for="birthday" translate="ORDERING.DAY_OF_BIRTHDAY"></label>
              </div>

              <div class="col-12 my-3">
                <span class="fw-bold font-size-12" translate="ORDERING.PAYMENT_METHOD"></span>
              </div>
              <div class="col-12 mb-2 mobile-payment">

                <label class="mt-2 ms-2" for="flexRadioDefault6">
                  <input class="margin-start-none" formControlName="payment_type" type="radio" value="cash"
                         name="payment_type" id="flexRadioDefault6">
                  <span class="ms-2" translate="ORDERING.CASH"></span>
                </label>

                <label *ngIf="countCityService.cityModule?.online_payment?.status && this.preorderDatepickerSettings().enabled" class="mt-2 ms-2" for="flexRadioDefault7">
                  <input *ngIf="countCityService.cityModule?.online_payment?.status"
                         formControlName="payment_type" class="ms-3 margin-start-none" type="radio" value="online"
                         name="payment_type"
                         id="flexRadioDefault7">
                  <span class="ms-2" translate="ORDERING.ONLINE"></span>
                </label>

                <label *ngIf="countCityService.cityModule?.card?.status" class="mt-2 ms-2" for="flexRadioDefault8">
                  <input formControlName="payment_type" class="ms-3 margin-start-none" type="radio" value="card"
                         name="payment_type" id="flexRadioDefault8">
                  <span class="ms-2" translate="ORDERING.CARD"></span>
                </label>
              </div>

              <div *ngIf="profileData.Authorization && countCityService.cityModule?.bonuses?.status === 1" class="col-12 my-3">
                <input type="checkbox" id="wantToPayByBonus" (change)="changeFlagBuyByBonuses($event)">
                <label class="ms-2 small-gray-font" for="wantToPayByBonus"  translate="ORDERING.WANT_PAY_BY_BONUSES"></label>
              </div>

              <div *ngIf="wantPayByBonus" class="col-12 mt-3 mb-2">
                <span class="fw-bold font-size-12" translate="BONUSES.PAY_WITH_BONUSES"></span>
              </div>
              <div *ngIf="wantPayByBonus" class="col-12 mt-3 mb-2">
                <div class="d-flex justify-content-center align-items-center">
                  <input pattern="[0-9]+" formControlName="bonuses" [max]="Number(profileData.bonusesBalance?.balance || 0)" min="0"
                         class="search w-100 hover" type="number">
                  <span class="ms-4 fw-bold"><span translate="BONUSES.BONUSES"></span>: {{profileData.bonusesBalance?.balance}} {{countCityService.selectedCountry.currency}}</span>
                </div>

              </div>
            </form>
          </div>
        </div>
        <span *ngIf="(form.invalid) && form.dirty || preorderSetWithNoTimeSelected()"
              class="ms-4 font-size-12 error-hint" translate="ORDERING.FILL_ALL_FIELD"></span>

            <!-- ZONE-RELATED ERRORS AT THE BOTTOM OF THE FORM -->
          @if(addressIsVerified?.value && form.get('delivery_type')?.value === 'delivery'){
            <div class="zone-errors">
              @if(!addressDeliveryAvailable?.value){
                <span class="zone-errors__item">*{{"ZONE_COVERAGE.DELIVERY_UNAVAILABLE" | translate}}</span>
              }
              @if((cartLogicService.cartTotal || 0) < (addressZoneData?.value?.min_price || 0)) { <span class="zone-errors__item">
                  *{{"ZONE_COVERAGE.ORDER_TOTAL_UNDER_MIN" | translate :{minPrice: addressZoneData?.value?.min_price || 0} }}</span>
              }
            </div>
          }

        <button
          *ngIf="countCityService.cityInfo?.enabled === 1 && countCityService.cityInfo?.work_time === true"
          class="btn send-order mt-1"
          [disabled]="buttonDisabled()"
          (click)="sendOrder()">
          <img width="34" class="me-2" src="../../../assets/delivery.webp" alt="Delivery image" loading="lazy">
          <span class="send-order__text" translate="ORDERING.SEND_ORDER"></span>
        </button>
      </div>
    </div>
  </div>
</div>

<ng-template #cardProduct>
  <div class="modal-header none-border">
    <h4 class="modal-title"></h4>
    <svg style="cursor: pointer" (click)="closeModal()" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
         fill="#E12829" class="bi-x-lg" viewBox="0 0 16 16">
      <path fill-rule="evenodd"
            d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"/>
      <path fill-rule="evenodd"
            d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"/>
    </svg>
  </div>
  <div class="modal-body">
    <app-product-card [item]="productId"></app-product-card>
  </div>
</ng-template>

<ng-template #recommendedProduct let-modal >
  <app-recommended-modal [firstFourCards]="firstFourCards" (closeModal)="closeModal()" [country]="countCityService.selectedCountry" [recommended]="recommended"></app-recommended-modal>
  <!-- (getCart)="getCart()" -->
</ng-template>

<ng-template #blacklistedPhone>
  <div class="modal-header none-border">
    <svg style="cursor: pointer" (click)="closeModal()" xmlns="http://www.w3.org/2000/svg" width="16" height="16"
      fill="#E12829" class="bi-x-lg" viewBox="0 0 16 16">
      <path fill-rule="evenodd"
        d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
      <path fill-rule="evenodd" d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
    </svg>
  </div>
  <div class="modal-body">
    <p translate="ORDER_ERROR.BLACKLIST.MESSAGE"></p>
    <p translate="ORDER_ERROR.BLACKLIST.CONTACTS" [translateParams]="{phone:supportPhoneNumber}"></p>
  </div>
</ng-template>

<ng-template #mapRef let-isDesk="isDesk">

  <div class="delivery-wrapper" [ngClass]="{'delivery-map-wrapper--mobile':!isDesk}">
    <app-map [mapId]="mapId" [coordinates]="addressCoordinates?.value" [options]="{fullscreenControl:false, streetViewControl:false}" (moveMarker)="handleMarkerMove($event)"></app-map>
    <div class="custom-delivery">
      @let locationInput = {address:addressName?.value, city: form.get('address')?.get('city')?.value};
      <app-locations-dropdown-option [location]="locationInput!" [showTooltip]="true"></app-locations-dropdown-option>
      <button (click)="confirmDeliveryAddress(isDesk)" class="btn custom-delivery__button" [disabled]="(form.hasError('deliveryUnavailable')) && form.get('address')?.touched">
        {{"ZONE_COVERAGE.CONFIRM_ADDRESS" | translate}}
      </button>
    </div>
  </div>

</ng-template>


<ng-template #locationAutocompleteRef let-isModal="isModal">
  @if(locationDropdownOptions() && !locationDropdownOptions()?.length){
  <div class="locations-list delivery-wrapper">
    <div class="locations-list__item">
      <app-locations-dropdown-option [location]="undefined"></app-locations-dropdown-option>
    </div>
  </div>
  }
  @else if(locationDropdownOptions() && locationDropdownOptions()?.length){
  <div class="locations-list delivery-wrapper" [ngClass]="{'delivery-wrapper--mobile':isModal}">
    @for (location of locationDropdownOptions(); track location.id) {

    <button (click)="selectLocation(location, isModal, mapRef)" class="locations-list__item">
      <app-locations-dropdown-option [location]="location"></app-locations-dropdown-option>
    </button>
    }
  </div>
  }
</ng-template>

<ng-template #invalidAddressRef>
  {{addressRefErrorText()! | translate}}
</ng-template>

<app-toasts></app-toasts>


