import {Component, EventEmitter, HostListener, Input, OnInit, Output, TemplateRef} from '@angular/core';
import {Recomended} from "../../interfaces/ordering.interface";
import {Unsubscribe} from "../../../../unsubscribe";
import {Country} from "../../../../shell/components/header/interfaces/city-info.interface";
import {StorageLogicService} from "../../../../shared/utils/services/storage-logic.service";
import { CartRestService } from 'src/app/shell/components/navbar/cart/services/cart-rest.service';
import { CartLogicService } from 'src/app/shell/components/navbar/cart/services/cart-logic.service';

@Component({
  selector: 'app-recommended-modal',
  templateUrl: './recommended-modal.component.html',
  styleUrls: ['./recommended-modal.component.scss']
})
export class RecommendedModalComponent extends Unsubscribe implements OnInit {

  @Input() firstFourCards!: Recomended[];
  @Input() cardProduct!: TemplateRef<Recomended[]>;
  @Input() country!: Country;
  @Input() recommended!: Recomended[];
  @Output() closeModal: EventEmitter<void> = new EventEmitter<void>();
  @Output() getCart: EventEmitter<void> = new EventEmitter<void>();
  constructor(
    public cartService: CartRestService,
    private cartLogicService: CartLogicService,
    public storageService: StorageLogicService
  ) {
    super();
  }

  ngOnInit(): void {
    this.checkWindowSize();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    this.checkWindowSize();
  }

  checkWindowSize(): void {
    if (window.innerWidth >= 768) {
      this.closeModal.emit();
    }
  }

  addRecommededToCart(id: number, index: number){
    this.recommended[index].hidden = true;
    this.addToCartMobile(id);
  }

  addToCartMobile(index: number): void {
    this.cartLogicService.addProduct(index);
  }

  triggerCloseModal(): void {
    this.closeModal.emit();
  }

}
