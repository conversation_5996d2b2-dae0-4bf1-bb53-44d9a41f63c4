  <div class="modal-body padding row">
    <div class="product-section">
      <img src="./assets/shef.webp" alt="shef" class="product-image">
      <div class="product-info">
        <h5 class="text-center font-size-15 color-h5" translate="ORDERING.CHEF_RECOMMENDS"></h5>
      </div>
    </div>
    <div class="grid">
      <div class="col-md-6" *ngFor="let card of firstFourCards; index as i;">
        <div class="card product-card text-center mt-4 mb-4" dimensions>
          <div class="white-container">
          <div class="weight">
            <div>
              {{card?.price?.weight}}
            </div>
          </div>
          <img [attr.data-src]="card?.price?.img"
                class="card-img-top d-block mx-auto product-img"
                alt="...">
          </div>
          <div class="card-body">
            <div class="name">
              <h5 class="product-name">{{card?.name}}</h5>
            </div>
            <p class="price mt-2 d-flex justify-content-center align-items-baseline">{{card?.price?.price | number: '1.0-2'}} <span>{{country.currency}}</span></p>
            <button class="btn green-button product-button" [disabled]="card?.hidden" (click)="addRecommededToCart(card.id, i)">
              <img style="width: 20%" src="./assets/icons/cart-white.webp" alt="cart">
              {{'TO_CART' | translate}}
            </button>
          </div>
        </div>
      </div>
    </div>
    <button class="btn btn-success" (click)="triggerCloseModal()" translate="ORDERING.CONTINUE"></button>
  </div>

<ng-container *ngTemplateOutlet="cardProduct"></ng-container>

