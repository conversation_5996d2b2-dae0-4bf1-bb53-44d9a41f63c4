@import '../recommended-products/recommended-products.component';

@media screen and (max-width: 768px) {
  .price{
    color: var(--text-accent);
    font-weight: bold;
    span{
      font-size: 15px;
      margin-left: 2px;
    }
    @media (prefers-color-scheme:dark) {
      color: var(--text-primary);
    }

  }

  .product-section {
    margin: 30px 0 0 0;
  }

  .product-image {
    left: 50px;
  }

  .white-container {
    border-radius: 20px 20px 0 0;
    background: white;
  }

  .grid{
    display: grid;
    align-items: center;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    margin: 15px 0;
    .col-md-6{
      width: 100%;
    }
  }
}

@media screen and (max-width: 480px) {
  .grid{
    display: grid;
    align-items: center;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10px;
    margin: 15px 0;
  }

  .product-card{
    margin: 0!important;
  }

  .product-image {
    left: 20px;
    top: -37px;
  }

  .product-section {
    margin: 10px 0;
  }
  .padding{
    padding: 25px;
  }

  .price{
    margin-top: 0!important;
  }

  .green-button{
    img{
      margin-right: 3px;
    }
  }
}

@media only screen and (min-device-width: 320px) and (max-device-width: 400px){
  .product-image {
    left: 10px;
    top: -39px;
  }

  .font-size-15{
    font-size: 15px;
  }

  .product-info{
    padding-left: 110px;
  }
}
