import {Component, EventEmitter, Input, OnInit, Output, TemplateRef} from '@angular/core';
import {animate, style, transition, trigger} from "@angular/animations";
import {takeUntil} from "rxjs";

import {Recomended} from "../../interfaces/ordering.interface";
import {Unsubscribe} from "../../../../unsubscribe";
import {Country} from "../../../../shell/components/header/interfaces/city-info.interface";
import {StorageLogicService} from "../../../../shared/utils/services/storage-logic.service";
import { CartRestService } from 'src/app/shell/components/navbar/cart/services/cart-rest.service';
import { CartLogicService } from 'src/app/shell/components/navbar/cart/services/cart-logic.service';

@Component({
  selector: 'app-recommended-products',
  templateUrl: './recommended-products.component.html',
  styleUrls: ['./recommended-products.component.scss'],
  animations: [
    trigger('slide', [
      transition(':increment', [
        animate('300ms', style({ transform: 'translateX(-1%)' })),
      ]),
      transition(':decrement', [
        animate('300ms', style({ transform: 'translateX(1%)' })),
      ]),
    ]),
  ],
})
export class RecommendedProductsComponent extends Unsubscribe implements OnInit{

  @Input() recommended!: Recomended[];
  @Input() displayedCards!: Recomended[];
  @Output() updateDisplayedCards: EventEmitter<number> = new EventEmitter<number>();
  @Output() openModal: EventEmitter<number> = new EventEmitter<number>();
  @Output() getCart: EventEmitter<number> = new EventEmitter<number>();
  @Input() country!: Country;
  @Input() cardProduct!: TemplateRef<Recomended[]>;
  @Input() currentIndex!: number;
  cardsAnimationState: number = 0;

  constructor(
    public cartService: CartRestService,
    private cartLogicService: CartLogicService,
    public storageService: StorageLogicService
  ) {
    super();
  }

  ngOnInit(): void {
  }



  showNextThreeCards(): void {
    this.currentIndex = (this.currentIndex + 1) % this.recommended.length;
    this.updateDisplayedCards.emit(this.currentIndex);
    this.cardsAnimationState++;
  }

  showPreviousThreeCards(): void {
    this.currentIndex = (this.currentIndex - 1 + this.recommended.length) % this.recommended.length;
    this.updateDisplayedCards.emit(this.currentIndex);
    this.cardsAnimationState--;
  }


  addToCart(index: number): void {
    this.cartLogicService.addFromRecommended(index)
    .subscribe(data => {
      const indexToRemove = this.recommended.findIndex(item => item.id === index);
      if (indexToRemove > -1) {
        this.recommended.splice(indexToRemove, 1);
        this.updateDisplayedCards.emit(this.currentIndex);
      }});
  }


  triggerOpenModal(productId: number): void {
    this.openModal.emit(productId);
  }
}
