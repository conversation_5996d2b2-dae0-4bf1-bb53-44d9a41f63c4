<div class="container-fluid" *ngIf="(recommended ?? []).length > 0">
  <div class="product-section">
    <img src="./assets/shef.webp" alt="shef" class="product-image">
    <div class="product-info">
      <h5 class="text-center font-size-15 color-h5" translate="ORDERING.CHEF_RECOMMENDS"></h5>
    </div>
  </div>
  <div class="row my-custom-carousel " >
    <button (click)="showNextThreeCards()" class="carousel-button next-button">
      <span class="circle-right"> ❮ </span>
    </button>
    <div class="col-md-4" *ngFor="let card of displayedCards" [@slide]="cardsAnimationState">
      <div class="card product-card text-center mt-4 mb-4" dimensions>
        <div class="white-container">
        <div class="weight">
          <div>
            {{card?.price?.weight}}
          </div>
        </div>
        <img (click)="triggerOpenModal(card?.id!)"  [attr.data-src]="card?.price?.img"
              class="card-img-top d-block mx-auto product-img"
              alt="...">
        </div>
        <div class="card-body">
          <div class="name">
            <h5 class="product-name">{{card?.name}}</h5>
          </div>
          <p class="price mt-2 d-flex justify-content-center align-items-baseline">
            <span>{{card?.price?.price | number: '1.0-2'}}</span>
            <span class="currency">{{country.currency}}</span>
          </p>
          <button class="btn green-button product-button" (click)="addToCart(card.id)">
            <img style="width: 20%" src="./assets/icons/cart-white.webp" alt="cart">
            {{'TO_CART' | translate}}
          </button>
        </div>
      </div>
    </div>
    <button  (click)="showPreviousThreeCards()" class="carousel-button prev-button ">
      <span class="circle-left"> ❯ </span>
    </button>
  </div>
</div>

<ng-container *ngTemplateOutlet="cardProduct"></ng-container>



