.font-size-15{
  margin-top: 0.5rem;
  font-size: 17px;
}

.product-section {
  position: relative;
  margin: 40px 40px 0 40px;
  background-color: #E7E7E7;
  border-radius: 20px;
  padding: 5px;
  box-shadow: -3px 3px 5px rgba(0, 0, 0, 0.2);
}

.white-container {
  border-radius: 20px 20px 0 0;
  background: white;
}

.product-image {
  max-height: 100px;
  position: absolute;
  top: -38px;
  left: 30px;
}

.product-info {
  padding-left: 120px;
}


.carousel-button {
  width: 0px!important;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  outline: none;
}

.prev-button {
  left: 18px;
  transform: rotate(180deg);
}

.next-button {
  right: -10px;
  transform: rotate(180deg);
  z-index: 1;
}

.my-custom-carousel {
  position: relative;
  padding:0 25px ;
}

.circle-left {
  color: #000;
  border-radius: 50%;
  padding: 5px 10px 5px 10px;
  text-align: center;
  font-size: 15px;
  background: #fff;
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.1);
}

.circle-right {
  color: #000;
  border-radius: 50%;
  padding: 5px 10px 5px 10px;
  text-align: center;
  font-size: 15px;
  background: #fff;
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.1);
}

.color-h5{
  font-weight: bold;
  color: #626971;
}

.my-custom-carousel {
  display: flex;
  overflow: hidden;
}

.product-card {
  background: #FFFFFF;
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  border: none;
  transition: all .3s ease;

  &:hover {
    transform: scale(1.03, 1.03);
  }
}

.weight {
  background: var(--primary);
  color: white;
  width: 2.4rem;
  height: 2.4rem;
  border-radius: 10rem;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 6%;
  top: 14%;
  transition: all .3s ease;
  &:hover {
    background: #0f8549;
  }
}

.product-img {
  cursor: pointer;
  width: 15rem;
  height: 10rem;
  object-fit: contain;
  transition: all 1s ease;
}

.product-name {
  font-weight: bold;
  font-size: 0.7rem;
  color: #656E76;
}

.name {
  height: 2rem;
}

.price {
  span {
    font-weight: bold;
    font-size: 1.0rem;
    color: var(--text-accent);

    @media (prefers-color-scheme: dark) {
      color: var(--text-primary);
    }

    &.currency {
      font-size: 0.8rem;
      margin-left: 3px;
    }
  }
}

.green-button {
  color: white;
  background: var(--primary);
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;
  transition: all .4s ease;

  &:hover {
    background: #12A95D;
  }
}

.product-img {
  padding-top: 10px;
  width: 6.5rem;
  height: 7rem;
}
.product-button {
  font-size: .7rem;
}

@media screen and (max-width: 1400px) {
  .my-custom-carousel {
    padding: 0;
  }

  .product-name {
    font-size: 0.6rem;
  }
}

@media screen and (max-width: 768px) {
  .container-fluid {
    display: none;
  }
}
