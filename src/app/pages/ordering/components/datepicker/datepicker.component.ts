import { Ng<PERSON><PERSON>, NgTemplateOutlet, TitleCasePipe } from '@angular/common';
import { Component, inject, input } from '@angular/core';
import { ControlContainer, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NgbDate, NgbDatepicker, NgbDatepickerModule, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { TranslatePipe } from '@ngx-translate/core';
import { DateTime } from 'luxon';

@Component({
  selector: 'app-datepicker',
  standalone: true,
  imports: [ReactiveFormsModule, NgbDatepickerModule, NgTemplateOutlet, NgClass, TranslatePipe, TitleCasePipe],
  templateUrl: './datepicker.component.html',
  styleUrl: './datepicker.component.scss'
})
export class DatepickerComponent{
  public disabledDates = input<string[]>([]);
  public daysAhead = input<number>(0);

  protected readonly parentFormGroup = inject(ControlContainer).control as FormGroup;

  get daysOff() {
    return this.disabledDates().map(date => {
      const [year, month, day] = date.split('-');
      return new NgbDate(+year, +month, +day);
    })
  }

  public get minDate(): NgbDate {
    const today = new Date();
    return new NgbDate(today.getFullYear(), today.getMonth() + 1, today.getDate());
  }

  public get maxDate():NgbDate {
    const maxDate =  DateTime.now().plus({ days: this.daysAhead() });
    return new NgbDate(maxDate.year, maxDate.month, maxDate.day);
  }

  public getMonthName(month: number): string {
    return DateTime.fromObject({ month }).setLocale('uk').toFormat('LLLL');
  }
  
  public isDayOff(date: NgbDate): boolean {
    return this.daysOff.some(
      (d) => d.year === date.year && d.month === date.month && d.day === date.day
    );
  }
  
  public navigate(datepicker: NgbDatepicker, number: number): void {
		const { state, calendar } = datepicker;
		datepicker.navigateTo(calendar.getNext(state.firstDate, 'm', number));
  }
  
  public canClickPrev(month: { month: number; year: number }): boolean {
    if (!this.minDate) {
      return true;
    }
    const min = new NgbDate(this.minDate.year, this.minDate.month, 1);
    const current = new NgbDate(month.year, month.month, 1);
    return current.after(min);
  }
  
  public canClickNext(month: { month: number; year: number }): boolean {
    if (!this.maxDate) {
      return true;
    }
    const max = new NgbDate(this.maxDate.year, this.maxDate.month, 1);
    const current = new NgbDate(month.year, month.month, 1);
    return current.before(max);
  }

  /** THIS NEEDS TO BE AN ARROW FUNCTION TO WORK */
  public readonly isDateDisabled = (date: NgbDateStruct): boolean => {
    const d = new NgbDate(date.year, date.month, date.day);
    return this.daysOff.some(off => off.equals(d));
  };
}
