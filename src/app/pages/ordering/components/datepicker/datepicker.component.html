 <div class="input-group" [formGroup]="parentFormGroup">
  <input
      class="form-control"
      [placeholder]="'PREORDER_DATEPICKER.PLACEHOLDER'|translate"
      name="dp"
      navigation="none"
      formControlName="preorder_date"
      ngbDatepicker
      #d="ngbDatepicker"
      [dayTemplate]="customDay"
      [contentTemplate]="customContent"
      [minDate]="minDate"
      [markDisabled]="isDateDisabled"
      [autoClose]="false"
  />
  <button class="btn d-flex calendar-button" (click)="d.toggle()" type="button">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-check" viewBox="0 0 16 16">
      <path d="M10.854 7.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 9.793l2.646-2.647a.5.5 0 0 1 .708 0"/>
      <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5M1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4z"/>
    </svg>
  </button>
</div>


<ng-template #customContent let-datepicker>
  <div class="datepicker">
	@for (month of datepicker.state.months; track month) {
    <div>
      <div class="datepicker__header">
        <div class="month">{{ getMonthName(month.month) | titlecase }} {{ month.year }}</div>
        <div class="arrows">
          <button type="button" class="arrow arrow--back" (click)="navigate(datepicker, -1)" [disabled]="!canClickPrev(month)">
            <ng-container
            *ngTemplateOutlet="arrow; context: { $implicit: canClickPrev(month), direction:'back' }"
          ></ng-container>
          </button>
          <button type="button" class="arrow arrow--forward" (click)="navigate(datepicker, 1)" [disabled]="!canClickNext(month)">  
            <ng-container
            *ngTemplateOutlet="arrow; context: { $implicit: canClickNext(month), direction:'forward' }"
          ></ng-container>
        </button>
        </div>
      </div>
      <ngb-datepicker-month class="border rounded" [month]="month"></ngb-datepicker-month>
    </div>
  }

  <div class="datepicker__footer">
    <div class="date date--unavailable"><span class="square"></span><span>{{"PREORDER_DATEPICKER.DATE_UNAVAILABLE" | translate}}</span></div>
    <div  class="date date--weekend"><span class="square"></span><span>{{"PREORDER_DATEPICKER.DATE_OFF" | translate}}</span></div>
  </div>
</div>
</ng-template>

<ng-template #customDay let-date let-currentMonth="currentMonth" let-selected="selected">
  <div
    class="datepicker__day"
    [class.active]="selected"
    [class.day-off]="isDayOff(date)"
  >
    {{ date.day }}
  </div>
</ng-template>

<ng-template #arrow let-enabled let-direction="direction">
  <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" [ngClass]="['arrow-icon', direction]">
    <path d="M0 6.5L1.14969 7.64969L5.6875 3.11188V13H7.3125V3.11188L11.8503 7.64969L13 6.5L6.5 0L0 6.5Z" [attr.fill]="enabled? '#35753A':'#656E76'"/>
    </svg>    
</ng-template>