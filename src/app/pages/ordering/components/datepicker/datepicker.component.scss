:host{
    --clr-day-off:#C74C4D;

    .input-group{
        border: none;
        border-radius: 2rem;
        background-color: var(--input-background);
        width: 100%;

        input{
            padding: 0;
            background: transparent;
            border: none;
            box-shadow: none;
            text-align: center;
            color: var(--input-color);
            width: 100%;

            &::placeholder{
                color: inherit;
            }
        }

        .calendar-button{
            padding: 0;
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .datepicker{
        ::ng-deep{
            .ngb-dp-weekdays {
                border-bottom: none;
                border-radius: 0;
                background-color: transparent;
            }

            .ngb-dp-weekday {
                font-style: normal;
                font-weight: bold;
                color: var(--text-primary);
            }

            .ngb-dp-day.disabled{
                color: #BCBCBC;
            }

            .dropdown-menu{
                width: 100% !important;
                display: block;
            }

            .border{
                border: none !important;
            }
        } 

        padding: 20px 24px;

        &__header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .month{
                color:var(--text-title);
                font-weight: bold;
            }
    
            .arrows{
                .arrow{
                    background-color: transparent;
                    border: none;
                }

                .arrow-icon{
                    &.forward{
                        rotate:180deg;
                    }
                }
            }
        }

        &__day{
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.185rem 0.25rem;
            border-radius: 0.25rem;
                  
            &:hover {
                background-color: #e0f7e9;
                color: #0f5132;
            }
            
            &.day-off {
                color: var(--clr-day-off);
            }
                  
            &.active {
                background-color: var(--primary);
                color: white;
            }
        }

            &__footer{
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin-top: 24px;
    
                .date{
                    display: flex;
                    align-items: center;
                    gap: 4px;
    
                    .square{
                        width: 12px;
                        height: 12px;
                        border-radius: 4px;
                        display: block;
                    }
    
                    &--unavailable{
                        
                        color: #BCBCBC;
    
                        .square{
                            background-color: #BCBCBC;
                        }
                    }
                    &--weekend{
                        
                        color:var(--clr-day-off);
    
                        .square{
                            background-color: var(--clr-day-off);
                        }
                    }
                }
            }
        }


        @media (prefers-color-scheme: dark) {
            .input-group{
                background-color: var(--dark-theme-input-background);
                
                input{
                    color: var(--dark-theme-text-accent);
                }

                .calendar-button{
                    svg{
                        fill:var(--dark-theme-sprimary-color);
                    }
                }

                ::ng-deep .dropdown-menu{
                    --bs-dropdown-bg: var(--dark-theme-primary-background) !important;
                    --bs-dropdown-color: var(--dark-theme-sprimary-color) !important;
                }

                ::ng-deep .ngb-dp-header{
                    background-color: var(--dark-theme-primary-background);
                }
            }
          }
    }

