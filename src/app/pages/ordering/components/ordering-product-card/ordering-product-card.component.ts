import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CountdownService } from 'src/app/shared/utils/services/countdown.service';
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';
import { Products } from 'src/app/shell/components/navbar/cart/interfaces/cart.interface';


@Component({
  selector: 'app-ordering-product-card',
  templateUrl: './ordering-product-card.component.html',
  styleUrls: ['./ordering-product-card.component.scss'],
  providers: [CountdownService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderingProductCardComponent {

  @Input() product!: Products;

  @Output() addProduct = new EventEmitter<number>();
  @Output() deleteProduct = new EventEmitter<number>();
  @Output() deleteAllProduct = new EventEmitter<number>();

  constructor(public readonly countCityService:CountryCityNotificationService) {}
}
