<div class="row mx-3 py-3 justify-content-between">

    <div class="col-3 d-flex flex-wrap align-items-center" dimensions>
        <div class="position-relative">
            <img *ngIf="product.img" [attr.data-src]="product.img" class="card-img-top d-block mx-auto product-card-img"
                alt="Products image" loading="lazy" appImgStyle>
            <img *ngIf="!product.img" src="assets/sushi-like.webp" class="card-img-top d-block mx-auto product-card-img"
                alt="Sushi Like image" loading="lazy" appImgStyle>

            <img *ngIf="product.sale_until && product.sale_percent" src="assets/icons/discount.webp" alt="discount icon" class="discount-icon">
        </div>

    </div>
    <div class="col-3 my-auto p-0">
        <span class="align-middle">{{product.name}}</span>
    </div>
    <div class="col-2 align-items-center d-flex p-0">
        <span style="cursor: pointer" class="fw-bold align-middle" (click)="deleteProduct.emit(product.id)">-</span>
        <span class="green-font-order align-middle mx-3">{{product.quantity}}</span>
        <span style="cursor: pointer" class="fw-bold align-middle" (click)="addProduct.emit(product.id)">+</span>
    </div>
    <div class="col-3 my-auto p-0 d-flex flex-column gap-1">
        <span *ngIf="product.price !== product.regular_price" class="sale-price__sum">{{product.quantity *
            product.regular_price!| number: '1.0-2'}} {{countCityService.selectedCountry.currency}}</span>
      <p class="fw-bold mb-0">
        <span class="fs-18">{{product.quantity * product.price! | number: '1.0-2'}}</span>
        <span class="fs-14"> {{countCityService.selectedCountry.currency}}</span>
      </p>
        <span class="d-block font-size-12">{{product.price | number: '1.0-2'}}
            {{countCityService.selectedCountry.currency}} <span translate="ORDERING.PER_PIECE"></span></span>
    </div>
    <div class="col-1 my-auto">
        <svg style="cursor: pointer" (click)="deleteAllProduct.emit(product.id)" xmlns="http://www.w3.org/2000/svg" width="16"
            height="16" fill="#E12829" class="bi-x-lg" viewBox="0 0 16 16">
            <path fill-rule="evenodd"
                d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
            <path fill-rule="evenodd"
                d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
        </svg>
    </div>
</div>
