input[type=checkbox] {
  accent-color: var(--primary);
}

.fs-16 {
  font-size: 16px;
}

.fs-18 {
  font-size: 18px;
}

.fs-14 {
  font-size: 14px!important;
}

.free-product-block {
  padding-top: 1rem;
  padding-bottom: 1rem;
}


.free-product {
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
  align-items: center;

  &__image {
    width: 4rem;
    height: 4rem;
    object-fit: contain;
  }

  &__name {
    text-align: right;
    font-size: 14px;
  }

  &__per-piece {
    display: block;
    text-align: right;
    color: var(--text-accent);
    font-size: 12px;
  }
}

.quantity-price {
  display: flex;
  justify-content: space-between;
  padding-left: 1.5rem;
  align-items: center;

  &__total {
    padding-top: 4%;
    padding-bottom: 4%;
    padding-right: 1.5rem;
  }

  &__free {
    padding: 4% 4%;
    font-weight: bold;
    color: var(--marker-color);
    background: var(--marker-primary);
    border-radius: 10rem 0 0 10rem;
  }
}

.sale-price {
  font-size: 12px;
  margin: 0;

  &__sum {
    text-decoration: line-through;
    font-weight: bold;
    color: var(--text-primary)
  }
}

.drop-menu {
  width: 3.8rem;
  border: 2px solid var(--marker-secondary-color);
  border-radius: 10rem;
  box-shadow: none;
  min-width: 0;
  z-index: 0;
}

.drop-menu-filter {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  margin: 1rem 0;
  border: none;
  left: 1rem;
  top: 3rem;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: var(--button-secondary-color);
  padding: 4px 0;
  box-shadow: none;
  transition: all .2s ease;
}

.dropdown-menu {
  width: calc(100%);
}

.search {
  color: var(--input-color);
  border: none;
  border-radius: 2rem;
  padding: 0.2em 1em 0.4em 1em;
  background-color: var(--input-background);
  width: 100%;
}

.drop-menu-button:hover {
  width: calc(100% - 2rem);
  background: var(--button-primary);
  color: var(--button-color);
  border-radius: 10rem;
  margin: 1rem;
  transition: all .5s ease;
}

.ordering {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  line-height: 90%;
}

span {
  font-size: 14px;
  color: var(--text-primary);
}

.green-font-order {
  color: var(--text-accent);
  font-weight: bold;
}

.hr {
  height: 1px;
  background: var(--select);
}

.total {
  height: 3.3rem;
  background: var(--primary);
  border-radius: 0 0 20px 20px;
  bottom: 0;
  width: 100%;
}

.white-font {
  font-size: 16px;
  color: var(--main);
}

.small-red-font {
  color: var(--danger);
  font-size: 12px;
}

.send-order {
  background: var(--button-primary);
  border-radius: 0 20px 0 20px;
  height: 3.3rem;
  bottom: 0;
  width: 70%;
  color: var(--button-color);
  font-weight: bold;

  &__text {
    color: inherit;
  }
}

.search-textarea {
  height: 6rem;
  color: var(--input-color);
  border: none;
  border-radius: 1.5rem;
  padding: 1rem;
  background-color: var(--input-background);
}

input {
  &[type='radio'] {
    visibility: hidden;
  }
}

input[type='radio']:after {
  width: 16px;
  height: 16px;
  border-radius: 16px;
  position: relative;
  background-color: var(--input-radio-background);
  content: '';
  display: inline-block;
  visibility: visible;
  border: 2px solid var(--input-background);
}

input[type='radio']:checked:after {
  width: 16px;
  height: 16px;
  border-radius: 16px;
  position: relative;
  background-color: var(--input-radio-not-checked-border);
  content: '';
  display: inline-block;
  visibility: visible;
  border: 4px solid var(--input-radio-border);
}

textarea {
  resize: none;
}

::placeholder {
  font-size: .7em;
}

.small-gray-font {
  color: var(--text-primary);
  font-size: 12px;
}

.ng-valid.ng-dirty:not(form) {
  border: solid 1px var(--input-border-success);
}

.ng-invalid.ng-dirty:not(form), .ng-invalid:not(form).form-submitted {
  border: solid 1px var(--input-border-warning);
  color: var(--text-primary);
}

.hover {
  transition: all .3s ease;

  &:hover {
    background-color: var(--input-background-hover);
  }
}

input[type='text']:focus {
  outline: 2px solid var(--input-outline);
}

.input-width-68 {
  width: 68%;
  @media (max-width: 767px){
    width: 100%;
  }
}

.input-width-28 {
  width: 28%;
}

.input-width-48 {
  width: 48%;
}

.input-width-49 {
  width: 49%;
}

.input-width-25 {
  width: 25%;
}

.font-size-12 {
  font-size: 12px;
}

.font-size-10 {
  font-size: 10px;
}

.ordering-desktop {
  display: block;
}

.ordering-mobile {
  display: none;
}

.bonus {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  min-width: 30rem;
  width: 15%;
  height: 6rem;

  &__quantity {
    font-size: 20px;
    font-weight: bold;
    color: var(--bonus-quantiy);
  }

  &__title {
    font-size: 16px;
  }

  &-close {
    color: var(--bonus-icon-close);
    border: none;
    border-radius: 10rem;
    width: 2rem;
    height: 2rem;
    background-color: var(--bonus-icon-background);
    position: absolute;
    left: 100%;
    top: 0;
    margin-left: 1rem;
  }

  &-button {
    border: none;
    margin: .5rem;
    color: var(--button-color);
    background: var(--button-primary);
    border-radius: 10rem;
    transition: all .4s ease;

    &:hover {
      background: var(--button-primary-hover);
    }
  }
}

.gift-product {
  &__order {
    display: flex;
    align-items: center;
  }

  &__checkbox-wrapper {
    display: flex;
    justify-content: center;
    flex: 1;
  }

  &__grayscale {
    filter: grayscale(1);
  }
}

.product-card-img {
  object-fit: contain;
  width: 5rem;
  height: 5rem;
}

@media screen and (max-width: 1400px) {
  .total {
    height: auto;
  }
  .white-font {
    margin: .5rem 0;
  }
  .quantity-price {
    &__free {
      font-size: 13px;
    }
  }
}


@media screen and (max-width: 768px) {

  .bonus {
    min-width: 21rem;

    &-close {
      margin-left: 0;
      left: 90%;
      top: -45%;
    }
  }

  .mobile-payment {
    display: flex;
    flex-direction: column;
  }

  .margin-start-none {
    margin-left: 0 !important;
  }

  .mobile-massage {
    word-break: break-word;
    width: 9rem;
  }
  .input-width-40 {
    width: 100%;
  }
  .input-width-28 {
    width: 49%;
  }
  .send-order {
    background: var(--button-primary);
    border-radius: 0 0 20px 20px;
    height: 3.3rem;
    bottom: 0;
    width: 100%;
    color: var(--button-color);
    font-weight: bold;
  }
  .input-width-25 {
    width: 49%;
  }

  .error-hint {
    color: #c0304a;
    text-align: center;
  }

  .input-width-48 {
    width: 49%;
  }
  .free-product-mobile {
    flex: 0 0 auto !important;
    width: 100% !important;
    padding-bottom: 1rem !important;
  }
  .col-5 {
    flex: 0 0 auto;
    width: 100%;
    padding-bottom: 1rem;
  }
  .col-7 {
    flex: 0 0 auto;
    width: 100%;
    padding-bottom: 1rem;
  }
  .ordering-desktop {
    display: none;
  }
  .ordering-mobile {
    display: block;
  }

  .product-card-img {
    width: 4rem;
    height: 4rem;
  }

  .green-font-order {
    margin: 0 10px !important;
  }
}

@media screen and (max-width: 460px){
  .minimize-margin {
    text-align: left;
    display: flex;
    align-items: center;

    &::after{
      margin-left: auto !important;
    }

    .address-ellipse {
      display: block;
      padding: 2px;
    }

    .green-font-order:first-of-type{
      margin: 0 !important;
    }

    .green-font-order:not(:first-of-type){
      display: none;
    }
  }
}

.delivery-wrapper{
  background-color: var(--block-background);
  position: absolute;
  z-index: 10;
  width: 100%;
  margin-top: 12px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;

    app-map {
      height: 200px;
    }


  @media (prefers-color-scheme:dark) {
    background-color: var(--dark-theme-primary-background);
  }
}

.delivery-map-wrapper--mobile{
  background-color: var(--block-background);
  width: 100%;
  position: static;
  margin: 0;

    app-map {
      height: 500px;
    }
}

.locations-list {
    padding-bottom: 12px;
    display: flex;
    flex-direction: column;

      &__item {
        border: none;
        background-color: transparent;
        padding-inline: 14px;

        app-locations-dropdown-option {
          display: block;
          padding-block: 8px;
          border-bottom: 1px solid #C4C4C4;
        }

        &:hover {
          background-color: rgba(18, 169, 93, 0.11);
        }
      }
}

.custom-delivery {
  display: flex;
  justify-content: space-between;
  padding: 24px;

  &__button{
    color:var(--main);
    background: var(--button-primary);
    border:none;
    border-radius: 10rem;
    font-size: 14px;
    padding: 8px 12px;
  }
}

.zone-errors{
  margin-block: 24px;
  padding-inline: 24px;

  &__item{
    color: #c0304a !important;
    font-size: 12px;
  }
}

@media (max-width:767px){
  .custom-delivery{
    padding: 12px 8px;
    flex-direction: column;
    gap: 12px;
  }
}
