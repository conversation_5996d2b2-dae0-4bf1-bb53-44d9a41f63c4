

import {AbstractControl, ValidationErrors, ValidatorFn} from '@angular/forms';

export function isAddressValid(): ValidatorFn {

    return (control: AbstractControl): ValidationErrors | null => {
        const is_house = control.get('isHouse');
        const addressFormGroup = control.get('address');
        const is_route = addressFormGroup?.get('is_route');
        const address_id = addressFormGroup?.get('address_id');
        const address_name = addressFormGroup?.get('adddress_name');

        if (address_name?.value && !address_id?.value) {
            addressFormGroup?.reset();
        }

        if (!address_id?.value) {
            return null;
        }

        if ((is_route?.value && !is_house?.value)) {
            return { deliveryUnavailable: true };
        }

        return null;
    }
}