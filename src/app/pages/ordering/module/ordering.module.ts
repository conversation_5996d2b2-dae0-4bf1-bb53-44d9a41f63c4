import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderingComponent } from "../components/ordering.component";
import { RouterModule, Routes } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ngb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgbDatepickerI18n, NgbDatepickerModule, NgbDropdownModule, NgbProgressbarModule, NgbTooltip } from "@ng-bootstrap/ng-bootstrap";
import { OrderingRestService } from "../services/ordering-rest.service";
import { TranslateModule } from "@ngx-translate/core";
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from "ngx-mask";
import { Shell } from "../../../shell/shell.service";
import { OrderSuccessComponent } from "../../order-success/components/order-success.component";
import { ProductCardModule } from "../../../shared/product-card/product-card.module";
import { RecommendedModalComponent } from "../components/recommended-modal/recommended-modal.component";
import { RecommendedProductsComponent } from "../components/recommended-products/recommended-products.component";
import { DimensionsModule } from "../../../shared/directives/dimensions.module";
import { IsUserBirthdayResolver } from '../services/is-user-birthday.service';
import { CountdownCounterModule } from "../../../shared/countdown-counter/module/countdown-counter.module";
import { OrderingProductCardComponent } from '../components/ordering-product-card/ordering-product-card.component';
import { MapComponent } from "../../../shared/map/map.component";
import { LocationsDropdownOptionComponent } from "../../../shared/locations-dropdown-option/locations-dropdown-option.component";
import { ToastsContainer } from 'src/app/toastes';
import { DatepickerComponent } from "../components/datepicker/datepicker.component";
import { DatepickerI18nService } from '../services/datepicker-i18n.service';
import { DatepickerDateAdapterService } from '../services/datepicker-date-adapter.service';

const routes: Routes = Shell.childRoutes([
  {
    path: '',
    component: OrderingComponent,
    resolve: {
      isUserBirthday:IsUserBirthdayResolver
    }
  },
  {
    path: 'order-success',
    component: OrderSuccessComponent
  }
])

@NgModule({
  declarations: [
    OrderingComponent,
    OrderSuccessComponent,
    RecommendedProductsComponent,
    RecommendedModalComponent,
    OrderingProductCardComponent,
  ],
  providers: [
    OrderingRestService, provideNgxMask(),
    { provide: NgbDatepickerI18n, useClass: DatepickerI18nService },
    { provide: NgbDateParserFormatter, useClass: DatepickerDateAdapterService },
  ],
    imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    NgbDatepickerModule,
    FormsModule,
    TranslateModule,
    NgbDropdownModule,
    NgxMaskDirective,
    NgxMaskPipe,
    NgbProgressbarModule,
    ProductCardModule,
    DimensionsModule,
    CountdownCounterModule,
    MapComponent,
    LocationsDropdownOptionComponent,
    ToastsContainer,
    NgbTooltip,
    DatepickerComponent
]
})
export class OrderingModule { }
