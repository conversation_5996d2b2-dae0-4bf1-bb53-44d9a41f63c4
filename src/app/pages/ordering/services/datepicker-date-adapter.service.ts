import { DestroyRef, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { DateTime } from 'luxon';
import { startWith } from 'rxjs';
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';


@Injectable()
export class DatepickerDateAdapterService extends NgbDateParserFormatter {
  private readonly langService = inject(CountryCityNotificationService);
  private readonly destroyRef = inject(DestroyRef);
  private locale = signal<string|null>(null);
  private readonly DELIMITER = '/';
  
  constructor() {
    super();

    this.observeLanguageChanges();
  }

  private observeLanguageChanges() {
    this.langService.languageChanged$.pipe(
      startWith(this.langService.lang$),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(locale => { 
      this.locale.set(locale === 'ua' ? 'uk' : locale);
    });
  }
  
	parse(value: string): NgbDateStruct | null {
		if (value) {
			const date = value.split(this.DELIMITER);
			return {
				day: parseInt(date[0], 10),
				month: parseInt(date[1], 10),
				year: parseInt(date[2], 10),
			};
		}
		return null;
	}

  format(date: NgbDateStruct | null): string {
    if (!date) {
      return '';
    }

    return DateTime.fromObject({
      day: date.day,
      month: date.month,
      year: date.year
    })
      .setLocale(this.locale() || 'uk')
      .toFormat('d LLLL yyyy');
	}
}
