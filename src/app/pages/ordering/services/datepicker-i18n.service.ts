import { Injectable } from '@angular/core';
import { NgbDatepickerI18n } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class DatepickerI18nService extends NgbDatepickerI18n {

  private weekdays: string[] = [];
  private months: string[] = [];

  constructor(private translate: TranslateService) {
    super();

    this.translate.onLangChange.subscribe(() => {
      this.loadTranslations();
    });

    this.loadTranslations(); // Initial load
  }

  private loadTranslations() {
    this.translate.get('PREORDER_DATEPICKER.WEEKDAYS').subscribe(res => {
      this.weekdays = res;
    });

    this.translate.get('PREORDER_DATEPICKER.MONTHS').subscribe(res => {
      this.months = res;
    });
  }

  override getWeekdayLabel(weekday: number, width?: Exclude<Intl.DateTimeFormatOptions['weekday'], undefined>): string {
    return this.weekdays[weekday - 1] || '';
  }

  getWeekdayShortName(weekday: number): string {
    return this.weekdays[weekday - 1] || '';
  }

  getMonthShortName(month: number): string {
    return (this.months[month - 1] || '').substring(0, 3);
  }

  getMonthFullName(month: number): string {
    return this.months[month - 1] || '';
  }

  getDayAriaLabel(date: import('@ng-bootstrap/ng-bootstrap').NgbDateStruct): string {
    return `${date.day}-${date.month}-${date.year}`;
  }
}
