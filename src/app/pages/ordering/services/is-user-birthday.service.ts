import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { catchError, EMPTY, map, Observable, of } from 'rxjs';
import { ProfileDataservice } from '../../profile/services/profile-data.service';
import { ProfileRestService } from '../../profile/services/profile-rest.service';


export const IsUserBirthdayResolver: ResolveFn<boolean> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
  profileData: ProfileDataservice = inject(ProfileDataservice),
  profileRestService: ProfileRestService = inject(ProfileRestService)): Observable<boolean> => {

   const cityId = profileData.Authorization?.city_id;
    if (cityId) {
      return profileRestService.getUserBirthday(cityId)
        .pipe(
          catchError(() => EMPTY),
          map(res => res.success? res.data.available: false),
        )
    }
    return of(false);
}