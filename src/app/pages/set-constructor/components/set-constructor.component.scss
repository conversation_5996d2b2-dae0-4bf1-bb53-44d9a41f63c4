
.constructor {
  gap: 45px;

  &__result {
    position: relative;

    .product-image-container {
      height: 80%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .product-image {
      display: grid;
    }

    .set-info {
      display: flex;
      gap: 5px;
      position: absolute;
      left: 0;
      bottom: 20%;

      &__size {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.6rem;
        padding: 0.6rem;
        color: #FFFFFF;
        border-radius: 10rem;

      }
    }
  }

  &__variants {
    width: 100%;
    position: relative;
    font-size: 1.5rem;
    background-color: var(--select);
    border-radius: 5px
  }
}

.button-confirmation {
  color: var(--button-select-color);
  &:hover {
    color: var(--primary);
  }
}

.instagram {
  width: 4rem;
  height: 4rem;
  cursor: pointer;
  border-radius: 10px;
}

.product-name {
  font-weight: bold;
  font-size: 1rem;
  color: var(--text-primary);
}

.instagram {
  width: 4rem;
  height: 4rem;
  cursor: pointer;
  border-radius: 10px;
}

.product-name {
  font-weight: bold;
  font-size: 1rem;
  color: var(--text-primary);
}

.information {
  padding: 3rem;
  p {
    color: var(--text-primary);
  }
}

.product-filter-container {
  margin-top: 1.5rem;
}

.close-btn {
  right: 12px;
  color: #FFFFFF;
  border: none;
  border-radius: 10rem;
  width: 1.8rem;
  height: 1.8rem;
  background-color: rgba(0, 0, 0, 0.2);
}

.close-products {
  position: absolute;
  top: 8px;
  right: 8px;
}

.close-btn-all {
  border: none;
  width: 1.8rem;
  height: 1.8rem;
  background-color: #FFFFFF;
}

.save-btn {
  background-color: #ECEFF0;
  color: #FFFFFF;
}

.add-btn {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(40%, -40%);
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 10rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--primary);
}

.add-icon {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FFFFFF;
}

.old-prise {
  text-decoration: line-through;
  color: #dbdbdb;
}

.prise {
  margin-right: 5rem;
}

.modal-body-container {
  max-width: 100%;
  position: relative;
  flex: 1 1 auto;
}


.green-button {
  color: white;
  background: var(--primary);
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;
  transition: all .4s ease;

  &:hover {
    background: #0f8549;
  }
}

.red-button {
  width: 100%;
  color: white;
  background: #e73f32;
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;
  transition: all .4s ease;

  &:hover {
    background: #a82f23;
  }
}

.price {
  font-weight: bold;
  font-size: 1.5rem;
  color: #656E76;

    &.price{
      @media (prefers-color-scheme: dark) {
        color: var(--dark-theme-secondary-color) !important;
      }
  }
}

.piece-img {
  width: 35px;
  height: 35px;
  filter: grayscale(1);
}

.piece {
  color: #5d656b;
}

.carousel-cell {
  margin-right: 10px;
  border-radius: 5px;
  counter-increment: gallery-cell;
}

/* cell number */
.carousel-cell:before {
  display: block;
  text-align: center;
  content: counter(gallery-cell);
  color: white;
}

.market {
  &-android {
    width: 8rem;
    margin-top: 1.5rem;
  }

  &-ios {
    width: 8rem;
    margin-top: 1.5rem;
  }
}

.phone {
  width: 10rem;
}

.warning {
  &-yes {
    width: 3rem;
    border-radius: 1rem;
    padding-top: .6rem;
    padding-bottom: .6rem;
    color: white;
    border: none;
    background: var(--primary);
  }

  &-no {
    width: 3rem;
    margin-left: 1rem;
    border-radius: 1rem;
    padding-top: .6rem;
    padding-bottom: .6rem;
    color: white;
    border: none;
    background: #c0304a;
  }
}

.name {
  height: 3rem;
}

.name-set {
  height: 3rem;
}

.product-card {
  background: #FFFFFF;
  box-shadow: 0 4px 28px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  border: none;
  transition: all .3s ease;

  &:hover {
    transform: scale(1.03, 1.03);
  }
}

.align-mobile {
  bottom: 0;
  justify-content: center;
  overflow: auto;
}

.info {
  width: 9%;
  height: 9%;
  margin: 1rem;
}

.weight {
  background: var(--primary);
  color: white;
  width: 3.8rem;
  height: 3.8rem;
  border-radius: 10rem;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 6%;
  top: 14%;
  transition: all .3s ease;

  &:hover {
    background: #0f8549;
  }
}

.weight-set {
  background: var(--primary);
  color: white;
  width: 3.8rem;
  height: 3.8rem;
  border-radius: 10rem;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 6%;
  top: 0;
  transition: all .3s ease;

  &:hover {
    background: #0f8549;
  }
}

.drop-menu {
  width: 3.8rem;
  border: 2px solid var(--primary);
  border-radius: 10rem;
  box-shadow: none;
  min-width: 0;
  z-index: 0;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: var(--primary);
  padding: 4px 0;
  box-shadow: none;
  transition: all .2s ease;
}

.drop-menu-button:hover {
  width: calc(100% - .4rem);
  background: var(--primary);
  color: #FFFFFF;
  border-radius: 10rem;
  margin: 1rem .2rem;
  transition: all .5s ease;
}

.none-border {
  border: none;
}

.loader {
  color: var(--primary);
  font-size: 10px;
  margin: 10rem auto;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  -webkit-animation: load4 1.3s infinite linear;
  animation: load4 1.3s infinite linear;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}

.text-bonus {
  font-size: 12px;
  color: #656E76;
}

.product-img-set {
  cursor: pointer;
  height: 10rem;
  object-fit: contain;
  transition: all 1s ease;
}

.modal-body {
  border: none;
  border-radius: 0;
}

.test {
  border-radius: 1000px;
}

.modal-background-blur {
  filter: blur(5px);
  pointer-events: none;
}

.constructor-wrapper {
  min-height: 50vh;
  display: flex;
  flex-direction: column;
}

.empty-block {
  color: var(--text-primary);
  font-weight: 700;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media screen and (max-width: 768px) {

  .constructor-wrapper {
    height: 80vh;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }

  .constructor {
    flex-direction: column;
    overflow: hidden;
    gap: 25px;

    &__result {
      position: relative;
      width: 100%;
      height: 50%;

      .product-image {
        top: 0 !important;
      }

    }

    &__variants {
      font-size: 1.2rem;
    }
  }
  .default-product-image {
    height: auto !important;
  }
  .set-info {
    bottom: 10% !important;

    &__size {
      padding: 0.3rem !important;
    }
  }
  .close-btn {
    top: 10px;
    right: 5px;
  }
  .filter {
    margin-top: 10px;
  }
  .product-img-set {
    height: 6rem;
  }
  .name-set {
    height: 3rem;
  }
  .information {
    padding: 0.5rem;
    p {
      color: var(--text-primary);
    }
  }
  .ingredients {
    display: none;
  }
  .bonuses {
    flex-direction: column;
  }

  .text-bonus {
    display: none;
  }
  .weight {
    width: 3.7rem;
    height: 3.7rem;
  }
  .weight-set {
    width: 3.7rem;
    height: 3.7rem;
  }

  .product-button {
    font-size: .9rem;
  }
  .card-padding {
    padding: .4rem;
  }
  .card-padding-right-left {
    padding-left: .4rem;
    padding-right: .4rem;
  }
  .carousel-cell {
    margin-right: 1rem;
    margin-left: 1rem;
    border-radius: 5px;
    counter-increment: gallery-cell;
  }
  .prise {
    margin-right: 0;
  }

  .red-button {
    padding-top: .2rem;
    padding-bottom: .2rem;
  }

  .price {
    font-size: 1.2rem;
    margin-bottom: .2rem;
  }
  .col-3 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-4 {
    flex: 0 0 auto;
    width: 50%;
  }
  .mt-4 {
    margin-top: 0 !important;
  }

}

@media screen and (max-width: 468px) {
  .weight {
    font-size: 10px;
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media screen and (max-width: 1200px) {

  .product-img {
    width: 10rem;
  }
}




