.container {
  height: 100vh;
  gap: 50px;

  .error-content {
    max-width: 400px;

    &__title {
      font-size: 100px;
      font-weight: 700;
      text-align: center;
      margin: 0;
    }

    &__status {
      font-size: 50px;
      letter-spacing: 15px;
      text-align: center;
      margin: 0;
    }

    &__message {
      text-align: center;
    }
    &__button {
      color: var(--button-color);
      background: var(--button-primary);
      border-radius: 1rem;
      padding-top: .6rem;
      padding-bottom: .6rem;
      transition: all .4s ease;

      &:hover {
        background: var(--button-primary-hover);
      }
    }
  }

  .error-img {
    width: 40%;
  }
}

@media screen and (max-width: 768px) {

  .container {
    height: 100vh;
    flex-direction: column;
    gap: 10px;

    .error-content {

      &__title {
        font-size: 30px;
      }

      &__status {
        font-size: 15px;
        letter-spacing: 3px;
      }

      &__message {
        font-size: 10px;
      }
    }

  }
}
