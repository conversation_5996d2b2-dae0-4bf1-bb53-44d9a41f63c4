.ordering {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  line-height: 90%;
  position: relative;
}

.form-switch {
  display: flex;
  align-items: center;
}

.form-switch .form-check-input {
  height: 24px;
  width: 48px;
  margin-right: 10px;
}

.form-switch .form-check-input:focus {
  border-color: rgba(0, 0, 0, 0.25);
  outline: 0;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba(0,0,0,0.25)'/></svg>");
}

.form-switch .form-check-input:checked {
  background-color: var(--input-color);
  border-color: var(--input-color);
  border: none;
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba(255,255,255,1.0)'/></svg>");
}

span {
  color: var(--text-primary);
  font-size: 14px;
}

.none-border {
  border: none;
}

.green {
  color: var(--text-accent);
}

.dot {
  height: 0.7rem;
  width: 0.7rem;
  border: 2px solid var(--secondary);
  border-radius: 10rem;
}

.small-gray-font {
  font-size: 10px;
  font-weight: bold;
}

.hr {
  height: 1px;
  background: var(--select);
}

.active-dot {
  height: 1rem;
  width: 1rem;
  border: 3px solid var(--primary);
  border-radius: 10rem;
}

.active-small-gray-font {
  font-size: 10px;
  font-weight: bold;
  color: var(--text-accent);
}

.history {
  border: 2px solid var(--select);
  border-radius: 20px;
}

.history-button {
  display: flex;
  flex-direction: row-reverse;
  font-size: 12px;
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 0 20px 0 20px;

  img {
    margin-right: 5px;
  }
}

.form-input {
  height: 2.5rem;
  color: var(--input-color);
  border: none;
  border-radius: 2rem;
  padding: 0.2em 1em 0.4em 1em;
  background-color: var(--input-background);
}

.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;
}

.tracking-mobile {
  display: none;
}

.tracking-desktop {
  display: flex;
}

.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 1rem;
  padding: .8rem;
  border: none;
}

.history-bonuses-icon {
  cursor: pointer;
  color: var(--bonus-icon-close);
  border-radius: 10rem;
  width: 2rem;
  height: 2rem;
  background-color: var(--bonus-icon-background);
}

.bonuses {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  height: 5rem;

  p {
    margin: 0;

  }

  &--text {
    &__red {
      color: var(--text-danger);
    }

    &__green {
      color: var(--text-accent);
    }

    &__grey {
      color: var(--text-primary);
    }
  }

  &:last-of-type {
    margin-bottom: 2rem !important;
  }
}

.empty-text {
  color: var(--text-primary);
  font-size: 14px;
  display: block;
  text-align: center;
}

.add-birthday {
  &__button {

      padding: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: none;
      border-radius: 100%;
      background: transparent;
      line-height: 1;
      font-weight: 700;
      color: var(--text-primary);
  }
}

@media screen and (max-width: 768px) {
  .ordering-mobile {
    flex-direction: column;
  }

  .mobile-align {
    text-align: center;
  }

  .dot {
    height: 1rem;
    width: 1rem;
    border: 3px solid var(--secondary);
    border-radius: 10rem;
  }

  .ordering-mobile-dot {
    display: flex;
    align-items: center;
    justify-content: center
  }

  .hr {
    width: 8rem;
  }

  .col-5 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-2 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-3 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-9 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-7 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-6 {
    flex: 0 0 auto;
    width: 100%;
  }

  .col-4 {
    flex: 0 0 auto;
    width: 100%;
  }

  .tracking-mobile {
    display: block;
  }

  .tracking-desktop {
    display: none;
  }

  .ms-4 {
    margin-left: 0 !important;
  }

  .margin-top-1 {
    margin-top: 1rem;
  }
}

#customBtn {
  display: inline-block;
  background: var(--google-button-background);
  color: var(--google-button-color);
  border-radius: 5px;
  border: thin solid var(--google-button-border);
  white-space: nowrap;
  margin-bottom: 24px;
}

#customBtn:hover {
  cursor: pointer;
}

span.label {
  font-family: serif;
  font-weight: normal;
}

span.icon {
  background: url('https://developers-dot-devsite-v2-prod.appspot.com/identity/sign-in/g-normal.png') transparent 5px 50% no-repeat;
  display: inline-block;
  vertical-align: middle;
  width: 42px;
  height: 42px;
}

span.buttonText {
  display: inline-block;
  vertical-align: middle;
  padding-right: 10px;
  font-size: 14px;
  font-weight: bold;
  /* Use the Roboto font that is loaded in the <head> */
  font-family: 'Roboto', sans-serif;
}

.tabs {
  display: flex;
  flex-direction: column;
  gap: 32px;

  @media (max-width: 768px) {
    & {
      gap: 12px;
    }
  }

  .tab {
    border: none;
    display: flex;
    align-items: center;
    height: 68px;

    &-active {
      background-color: var(--text-accent) !important;

      .change-fill path {
        fill: var(--block-background);
      }

      span {
        color: var(--block-background);
      }
    }
  }
}

.hidden {
  display: none;
}

.gender-form {
  position: absolute;
  z-index: 3;
  top: calc(100% + 12px);
  right: 0;
  min-width: 210px;
  width: 100%;
  max-width: 250px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 22px 12px;
  background-color: var(--block-background);

  &__label {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__label+&__label {
    margin-top: 23px;
  }

  &__hidden {
    display: none;
  }
}

.gender-toggle {
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  gap: 9px;

  &__chevron {
    transition: all 150ms ease-in-out;

    &-rotate {
      transform: rotate(90deg);
    }
  }
}

.padding {
  padding: 22px;
}

.bonus-products{
  &__header{
    display:flex;
    flex-direction: column;
  }
}

.tab-content {
  padding: 12px;
  color: var(--text-primary);
  line-height: 1.2;

  &__header {
    font-size: 21px;
    color: var(--text-primary);
    font-weight: bold;
    margin-bottom: 30px;
  }

  &__accent{
    font-size: 16px;
    font-weight: bold;
    @extend .green;
  }
}  

@media (min-width:769px){
  .tab-content {
    padding: 39px;
  
    &__header {
      font-size: 16px;
      color: var(--text-primary);
      font-weight: bold;
      margin-bottom: 12px;
    }
  
    &__accent{
      font-size: 14px;
    }
  } 
}

.referal__list{
  &>li{
    text-align: left;
  }
}

.referal{


  &__content {
      margin-top: 8px;
    ol {
      margin-bottom: 0;
    }

      display: flex;
      flex-direction: column;
      gap: 24px;

      @media (min-width: 769px) {
          gap: 42px;
      }
    }

  &__create-button{
    background-color:var(--text-accent);
    border-radius: 10px;
    border: none;
    padding: 14px 28px;
    display: flex;
    align-items: center;
    gap: 14px;
    margin-inline: auto;

    span{
      color: var(--block-background);
      font-weight: bold;
    }
  }
}

.referal-codes {
  p{
    margin: 0;
  }

  button{
    border: none;
    background-color: transparent;
  }

  padding:0;

  &__item{
    overflow: hidden;
    @media (max-width:768px){
      padding: 13px 0;
    }
    
    padding: 13px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
   
    border-bottom: 1px solid #C4C4C4;
  }
  .code-content {
    text-align: left;
    &__link {
      color: var(--text-primary);
    }

    &__status {
      color: orange;
    }
  }

  .non-selectable{
    user-select: none;
  }
}
.modal-header::ng-deep {
    display: flex;
    justify-content: space-between;
  }
  
.bonus-qty {
  cursor: pointer;
   
  &__clickable{
    display: flex;
    align-items: center;
    gap: 14px;
    width: fit-content;
  }

  &__icon-wrapper {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #C4C4C4;
    border-radius: 50%;
  }

  &__icon{
    width: 14px;
    height: 14px;
    color: var(--main);

    path {
      fill: var(--main);
    }
  }
}


.delivery-wrapper {
    background-color: var(--block-background);
    position: absolute;
    z-index: 10;
    width: calc(100% - 2* var(--bs-modal-padding));
    margin-top: 12px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;

    app-map {
      height: 200px;
    }


    @media (prefers-color-scheme:dark) {
      background-color: var(--dark-theme-primary-background);
    }
  }

  .delivery-map-wrapper--mobile {
    background-color: var(--block-background);
    width: 100%;
    position: static;
    margin: 0;

    app-map {
      height: 500px;
    }
  }

  .locations-list {
    padding-bottom: 12px;
    display: flex;
    flex-direction: column;


    &__item {
      border: none;
      background-color: transparent;
      padding-inline: 14px;

      app-locations-dropdown-option {
        display: block;
        padding-block: 8px;
        border-bottom: 1px solid #C4C4C4;
      }

      &:hover {
        background-color: rgba(18, 169, 93, 0.11);
      }
    }
  }

  .custom-delivery {
    display: flex;
    justify-content: space-between;
    padding: 24px;

    &__button {
      color: var(--main);
      background: var(--button-primary);
      border: none;
      border-radius: 10rem;
      font-size: 14px;
      padding: 8px 12px;
    }
  }

  .zone-errors {
    margin-block: 24px;
    padding-inline: 24px;

    &__item {
      color: #c0304a !important;
      font-size: 12px;
    }
  }

  @media (max-width:767px) {
    .custom-delivery {
      padding: 12px 8px;
      flex-direction: column;
      gap: 12px;
    }
  }

  .apple-button{
    @extend #customBtn;
      background-color: transparent;
      display: flex;
      padding: 0;
      width: 262px;

        align-items: center;

        &__bg-light {
            width: 42px;
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        span{
            display: inline-block;
            vertical-align: middle;
            padding-right: 10px;
            font-size: 14px;
            font-weight: bold;
            /* Use the Roboto font that is loaded in the <head> */
            font-family: 'Roboto', sans-serif;
        }
  }