import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ProductData } from 'src/app/pages/home/<USER>/home.interface';
import { DimensionsDirective } from 'src/app/shared/directives/dimensions.directive';
import { SortByPricePipe } from 'src/app/shared/pipes/sort-by-price.pipe';

@Component({
  selector: 'app-free-product-modal',
  templateUrl: './free-product-modal.component.html',
  styleUrls: ['./free-product-modal.component.scss'],
  providers:[SortByPricePipe, DimensionsDirective]
})
export class FreeProductModalComponent {
  @Input() public product!:ProductData;
  @Input() public userName:string='Guest'
  
  constructor(public activeModal: NgbActiveModal) { }

  dismiss() {
    this.activeModal.dismiss('Dismiss click');
  }
}
