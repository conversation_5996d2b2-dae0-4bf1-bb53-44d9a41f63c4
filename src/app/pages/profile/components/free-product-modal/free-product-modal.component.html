<div class="popup">

    <p translate="PROFILE.BONUS_PRODUCTS_MODAL" [translateParams]="{userName}" class="popup__header"
        id="dark-accent-text"></p>
    <button type="button" class="popup__close-button" aria-label="Close" (click)="dismiss()">
        <svg _ngcontent-hji-c38="" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#E12829"
            viewBox="0 0 16 16" class="bi-x-lg">
            <path _ngcontent-hji-c38="" fill-rule="evenodd"
                d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"></path>
            <path _ngcontent-hji-c38="" fill-rule="evenodd"
                d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"></path>
        </svg>
    </button>

    <div class="row">
        <div class="col-4 d-flex product-card" >
            <ng-container *ngIf="product.img">
                <img [src]="product.img" class="w-100 object-fit-contain"
                    alt="Product image" loading="lazy" width="320" appImgStyle/>
            </ng-container>
            <ng-template *ngIf="!product.img" class="loader">Loading...</ng-template>



        </div>
        <div class="col-8">
            <div class="d-flex justify-content-center">
                <h2 class="text-center" style="font-size: 23px; font-weight: bold">
                    {{ product.name }}
                </h2>
            </div>
            <div class="d-flex align-items-center justify-content-center mt-1">
                <div class="me-2">

                    <ng-container *ngIf="
                  product.prices?.length === 1 && product.prices?.length
                ">
                        <div class="weight-container my-auto fw-bold">
                            {{product.prices[0].weight}}
                        </div>
                    </ng-container>
                </div>
                <div *ngIf="
                product.prices[0]?.pieces !== 0 && product.prices?.length
              ">
                    <div class="pieces-container text-center">
                        <span class="pieces" translate="PRODUCT_CARD.PIECES"></span>:
                        {{ product.prices[0].pieces }}
                    </div>
                </div>
            </div>
            <div class="my-3 text-center">
                <span>{{ product.description }}</span>
            </div>
            <div class="py-2 text-center">
                <ng-container *ngFor="let object of product?.ingredients">
            
                        <img *ngIf="object.img" placement="top" [ngbTooltip]="object.name" class="ing"
                            [src]="object.img" alt="Ingredients" loading="lazy" width="65"
                            height="55" />
                        <img *ngIf="!object.img" placement="top" [ngbTooltip]="object.name" class="ing"
                            src="assets/sushi-like.webp" alt="Sushi like icon" loading="lazy" />
                  
                </ng-container>
            </div>
        </div>
    </div>
</div>
