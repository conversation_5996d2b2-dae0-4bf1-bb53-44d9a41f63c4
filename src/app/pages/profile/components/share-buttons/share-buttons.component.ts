import { trigger, state, style, transition, animate } from '@angular/animations';
import { Component, Input, OnInit, TemplateRef} from '@angular/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { Unsubscribe } from 'src/app/unsubscribe';
import { ToastService } from 'src/app/shared/utils/services/toast.service';
import { TranslateService } from '@ngx-translate/core';
import {  takeUntil } from 'rxjs';
import { StorageLogicService } from 'src/app/shared/utils/services/storage-logic.service';

@Component({
  selector: 'app-share-buttons',
  templateUrl: './share-buttons.component.html',
  styleUrls: ['./share-buttons.component.scss'],
  animations: [
    trigger('slideInOut', [
      state(
        'void',
        style({
          transform: 'translateX(100%)',
          opacity: 0,
        })
      ),
      state(
        '*',
        style({
          transform: 'translateX(0)',
          opacity: 1,
        })
      ),
      transition(':enter', [
        style({
          transform: 'translateX(100%)',
          opacity: 0,
        }),
        animate('250ms ease-in', style({
          transform: 'translateX(0)',
          opacity: 1,
        }))
      ]),
      transition(':leave', [
        animate(
          '350ms ease-out',
          style({
            transform: 'translateX(100%)',
            opacity: 0,
          })
        ),
      ]),
    ]),
  ],
})
export class ShareButtonsComponent extends Unsubscribe {
@Input() promocode! : string;

text : string = '';

showShareButtons = false;

darkTheme = this.storageService.getData('darkTheme');

  constructor(
    private clipboard: Clipboard,
    public toastService: ToastService,
    private translate: TranslateService,
    private storageService: StorageLogicService,
  ) {
    super();
  }
 
copyToClipboard(code: string) {
  const copied = this.clipboard.copy(code);
    if (copied) {
      this.translate.get('PROMOCODE.COPIED').pipe(
        takeUntil(this.$destroy),
      )
        .subscribe();
    }
}

showSuccess(template: TemplateRef<any>){
    this.toastService.show({
      template, 
      classname: 'bg-success text-light',
      delay: 2000,
      header: 'Success',
      body: 'PROMOCODE.COPIED'
    });
}

shareViaTelegram() {
  this.translate.get('ACTIVATE_PROMO.REQUEST').subscribe((res: string) => {
    this.text = res;
    window.open(`https://t.me/share/url?url=${this.promocode}&text=${this.text}`, '_blank');
  });
}

shareViaTwitter() {
  this.translate.get('ACTIVATE_PROMO.REQUEST').subscribe((res: string) => {
    this.text = res;
    window.open(`https://twitter.com/intent/tweet?url=${this.promocode}&text=${this.text}`, '_blank');
  });
}

shareViaFacebook() {
  this.translate.get('ACTIVATE_PROMO.REQUEST').subscribe((res: string) => {
    this.text = res;
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${this.promocode}&quote=${this.text}`, '_blank');
  });
}

shareViaViber() {
  this.translate.get('ACTIVATE_PROMO.REQUEST').subscribe((res: string) => {
    this.text = res;
  window.open(`viber://forward?text=${this.text} ${this.promocode}`, '_blank')
  });
}
}
