 <div class="buttons-wrapper">
 <button class="buttons-wrapper__action" (click)="showShareButtons=!showShareButtons">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14 3H21V10" stroke="#12A95D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    <path
      d="M21 14.7368V19.5C21 20.3285 20.3285 21 19.5 21H4.5C3.67158 21 3 20.3285 3 19.5V4.5C3 3.67158 3.67158 3 4.5 3H9"
      stroke="#12A95D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M12.9004 11.1L20.5504 3.44995" stroke="#12A95D" stroke-width="2" stroke-linecap="round"
      stroke-linejoin="round" />
  </svg>
</button>

<div *ngIf="showShareButtons" @slideInOut>
  <ng-container *ngTemplateOutlet="shareButtons"></ng-container>
    <ng-template #shareButtons>
    <div class="share-buttons">
        <button 
            type="button"
            class="share-buttons__button" 
            (click)="copyToClipboard(promocode);
                showSuccess(successTpl)"
        >
        <img src="assets/icons/copy.webp" alt="Copy button" width="24px" height="24px">
        </button>
        
        <button 
            type="button"
            class="share-buttons__button" 
            (click)="shareViaTelegram()"
        >
        <svg fill="none" height="24px" width="24px" viewBox="0 0 32 32" width="24" xmlns="http://www.w3.org/2000/svg">
                <path d="m0 16c0 8.8366 7.16344 16 16 16 8.8366 0 16-7.1634 16-16 0-8.83656-7.1634-16-16-16-8.83656 0-16 7.16344-16 16z" fill="#08c"/>
                <path d="m8.09992 15.7083c4.29498-1.8712 7.15898-3.1049 8.59198-3.7009 4.0915-1.7018 4.9416-1.9974 5.4958-2.0073.1218-.00205.3943.0282.5709.1714.149.1209.19.2843.2097.399.0196.1146.044.3759.0246.58-.2217 2.3296-1.1811 7.983-1.6692 10.5922-.2065 1.1041-.6132 1.4743-1.0069 1.5105-.8555.0787-1.5052-.5654-2.3339-1.1086-1.2967-.85-2.0292-1.3792-3.2879-2.2086-1.4546-.9586-.5116-1.4854.3174-2.3464.2169-.2253 3.9866-3.6541 4.0595-3.9652.0092-.0389.0176-.1839-.0685-.2605-.0862-.0765-.2133-.0503-.3051-.0295-.13.0295-2.2015 1.3987-6.2144 4.1075-.588.4038-1.1206.6005-1.5977.5902-.5261-.0114-1.53798-.2975-2.29022-.542-.92265-.2999-1.65596-.4585-1.5921-.9678.03326-.2653.3986-.5367 1.09604-.814z" fill="#fff"/></svg>
        </button>

        <button 
            type="button" 
            class="share-buttons__button"
            (click)="shareViaTwitter()"
        >
            <svg enable-background="new 0 0 1024 1024" height="24px" width="24px"  viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <circle cx="512" cy="512" fill="#1da1f2" r="512"/>
                <path d="m778 354.8c-18.8 8.3-38.9 13.9-60.1 16.5 21.6-13 38.2-33.5 46-57.9-20.2 11.8-42.7 20.4-66.5 25.2-19.1-20.4-46.2-33.2-76.4-33.2-57.8 0-104.7 46.9-104.7 104.6 0 8.3 1 16.3 2.7 23.9-87-4.1-164.2-45.9-215.8-109.1-9.1 15.4-14.2 33.2-14.2 52.7 0 36.4 18.5 68.4 46.6 87.2-17.2-.6-33.3-5.3-47.4-13.1v1.3c0 50.8 36 93.1 84 102.7-8.8 2.4-18.1 3.6-27.6 3.6-6.7 0-13.1-.6-19.5-1.8 13.4 41.6 52 71.9 98 72.7-35.7 28.1-81.1 44.8-129.8 44.8-8.3 0-16.6-.5-24.9-1.4 46.6 29.7 101.5 47 160.8 47 192.5 0 297.8-159.5 297.8-297.6 0-4.4 0-8.9-.3-13.4 20.4-14.7 38.3-33.2 52.3-54.2z" fill="#fff"/></svg>
        </button>

        <button 
            type="button"
            class="share-buttons__button"
            (click)="shareViaFacebook()"
        >
            <svg fill="none" height="24px" width="24px"  viewBox="0 0 92 92" xmlns="http://www.w3.org/2000/svg">
                <rect fill="#337fff" height="91.5618" rx="45.7809" width="91.5618"/>
                <path d="m56.9829 48.7613 1.3046-8.2888h-8.0362v-5.3877c0-2.2665 1.1219-4.4812 4.7095-4.4812h3.705v-7.0584c-2.1576-.3441-4.3378-.5302-6.5229-.5569-6.6141 0-10.9323 3.976-10.9323 11.164v6.3202h-7.3317v8.2888h7.3317v20.0486h9.0407v-20.0486z" fill="#fff"/></svg>
        </button>

        <button 
            type="button" 
            class="share-buttons__button"
            (click)="shareViaViber()"
        >
            <svg fill="none" height="24" width="24" viewBox="0 0 92 93" xmlns="http://www.w3.org/2000/svg">
                <rect fill="#754a91" height="91.5618" rx="45.7809" width="91.5618" y=".561523"/>
                <g fill="#fff"><path d="m35.0952 65.0596c0-.7675 0-1.535 0-2.3025.0054-.0314.0043-.0635-.0033-.0944-.0076-.031-.0215-.06-.0409-.0854s-.0438-.0466-.0718-.0623c-.0279-.0156-.0589-.0254-.0908-.0288-1.9199-.5404-3.6919-1.5031-5.184-2.8165-1.0872-.9799-1.9924-2.1413-2.6744-3.4312-.9613-1.8633-1.601-3.873-1.8927-5.9456-.3614-2.5999-.487-5.2266-.3751-7.8487.0246-1.2225.0876-2.431.2279-3.6535.2235-2.323.815-4.5967 1.7525-6.7374 1.1576-2.6321 3.2578-4.7464 5.8955-5.9352 1.7968-.8147 3.6871-1.4094 5.6292-1.7711 1.9044-.3575 3.8326-.5768 5.7693-.6564 1.7457-.0612 3.4934-.0357 5.2366.0764 2.4948.1043 4.9671.5138 7.3606 1.219 1.7746.5068 3.4628 1.2735 5.0088 2.2747 1.5063 1.0246 2.7245 2.4121 3.5401 4.032.9588 1.892 1.6106 3.922 1.9313 6.0151.2083 1.2427.3453 2.4961.4101 3.7542.0911 1.5593.0806 3.1256.021 4.678-.0596 1.5523-.1928 3.0596-.3855 4.5807-.2437 2.2831-.952 4.4935-2.0821 6.4978-1.4709 2.5354-3.8272 4.4501-6.6245 5.383-1.9128.6637-3.8878 1.136-5.8956 1.41-1.4686.2014-2.9407.3472-4.4199.4063-1.1321.0521-2.2642.0382-3.3964.0208-.6414 0-1.2793-.0625-1.9173-.1181-.0626-.0117-.1273-.0047-.1859.02-.0587.0247-.1086.0661-.1435.119-1.2899 1.535-2.6288 3.0283-3.9783 4.5147-.2458.2712-.5304.5052-.8447.6946-.2063.1341-.4416.2183-.6868.2457s-.4935-.0027-.7248-.0879-.4391-.223-.6067-.4025c-.1676-.1794-.2903-.3954-.3581-.6305-.1336-.4177-.1999-.8537-.1963-1.2919-.0035-.6876-.0035-1.3648-.0035-2.0421zm1.5597 2.9138.1648-.1528 2.9267-3.2194c1.1263-1.2386 2.2514-2.4785 3.3754-3.7194.0552-.0757.1298-.1356.216-.1734s.181-.0523.2747-.042c.382.0382.7641.0521 1.1497.0556 1.3319 0 2.6638 0 3.9957-.0938 1.2093-.0937 2.415-.2396 3.6138-.4271 1.2906-.2226 2.5683-.5136 3.8275-.8717 2.8041-.7467 4.9737-2.3442 6.3372-4.9315.8049-1.5907 1.3098-3.3138 1.4896-5.0843.2131-1.6688.3406-3.3472.3821-5.0288.0621-2.1124-.0835-4.2258-.4347-6.3102-.2435-1.5102-.6817-2.9832-1.3038-4.3828-.7922-1.7365-1.9138-3.1742-3.6418-4.098-2.6498-1.417-5.517-2.049-8.4718-2.372-.8377-.0903-1.6754-.1389-2.5166-.1702-1.7225-.088-3.4486-.0787-5.17.0278-1.238.091-2.4693.2557-3.6873.4932-1.7913.3235-3.5241.9087-5.142 1.7364-.9379.4696-1.7888 1.0931-2.5166 1.8441-1.1796 1.3063-2.0448 2.8606-2.5307 4.546-.5763 1.9629-.8909 3.9921-.9358 6.0359-.1128 2.0438-.087 4.0928.0771 6.1331.1021 1.2143.3073 2.4178.6134 3.5979.4554 1.8391 1.3389 3.547 2.5797 4.9871 1.3397 1.4693 3.0711 2.5335 4.9947 3.07.2419.073.3506.1563.3506.4272-.0211 1.0002 0 2.0004 0 3.0006z"/><path d="m34.2021 36.954c-.0246-.7605.3925-1.3127.8973-1.8094.6656-.6318 1.399-1.1896 2.1871-1.6635.3594-.2196.7867-.3032 1.2033-.2356s.7946.282 1.0645.6038c.9535.993 1.8069 2.0759 2.5482 3.2332.367.5206.6442 1.098.8202 1.7087.0703.2383.0749.4909.0132.7315-.0616.2407-.1872.4606-.3637.6368-.4044.3933-.8354.7588-1.2899 1.094-.229.1808-.3993.4244-.4897.7004-.0904.2761-.0968.5723-.0185.8519.2807 1.3131.9168 2.5256 1.8401 3.5077 1.122 1.3571 2.5676 2.4158 4.2061 3.0804.4026.2051.864.2676 1.3074.1771.2429-.0656.4587-.2054.6169-.3994.2453-.309.5117-.6077.7326-.9376.2864-.4116.7237-.696 1.2187-.7927.495-.0966 1.0085.0021 1.4311.2752 1.1881.683 2.3098 1.4736 3.3508 2.3616.2454.2083.5013.4098.7466.6216.2903.2194.5.5271.5969.8759.0968.3487.0755.7194-.0606 1.055-.2166.5645-.5322 1.0865-.9324 1.542-.4972.6848-1.1306 1.2615-1.8612 1.6948-.3492.2032-.7416.3225-1.1457.3484-.4042.026-.8089-.0422-1.1816-.1991-2.8359-1.1313-5.4856-2.6751-7.8619-4.5807-2.3242-1.8958-4.3554-4.1189-6.0287-6.5985-1.408-2.0527-2.5282-4.2853-3.3298-6.6367-.0854-.2844-.1556-.5731-.2103-.8648-.0156-.1267-.018-.2547-.007-.382z"/><path d="m46.3033 33.2589c2.3727.0435 4.6547.9106 6.4482 2.4503s2.9847 3.6542 3.366 5.975c.1535.8437.2356 1.6987.2454 2.556 0 .2014-.0526.3473-.2699.3681-.2173.0209-.2734-.1632-.2734-.3473.0326-1.7341-.3267-3.4534-1.0515-5.0322-.6104-1.2972-1.5146-2.4374-2.6423-3.3319s-2.4485-1.5191-3.8596-1.8253c-.7461-.1712-1.509-.2597-2.2748-.264-.0746.0013-.149-.008-.2208-.0277-.0612-.0171-.1133-.0571-.1454-.1115-.032-.0544-.0415-.1191-.0264-.1803.0005-.029.0067-.0577.0185-.0843.0117-.0266.0287-.0506.0499-.0706.0212-.0201.0462-.0357.0736-.0461.0274-.0103.0566-.0152.0858-.0143.1578-.0208.319-.0139.4767-.0139z"/><path d="m53.0695 42.6768c-.0118-.9648-.2516-1.9135-.7002-2.7699s-1.0935-1.5967-1.883-2.1616c-.9215-.6413-1.9927-1.0393-3.1125-1.1564-.1893-.0244-.3786-.0348-.5714-.0556-.1927-.0209-.3084-.1007-.2909-.2952s.1402-.264.3505-.2501c3.1055.1876 5.7483 1.9275 6.5615 5.1677.1487.6248.2229 1.2647.2208 1.9066v.1737c0 .1736-.1121.2882-.2839.2847-.0381-.0012-.0755-.0102-.11-.0263-.0345-.016-.0653-.0389-.0905-.0672-.0253-.0283-.0445-.0614-.0564-.0972-.0119-.0359-.0163-.0738-.013-.1114-.028-.1632-.021-.3299-.021-.5418z"/><path d="m47.6285 38.8083c1.7035.1146 2.8917.9412 3.2177 2.7366.0395.2301.0676.4619.0841.6946 0 .1876-.0421.3473-.2488.3473-.2068 0-.2769-.1389-.284-.3473-.0019-.6882-.2159-1.3595-.6133-1.924-.4177-.5112-1.0174-.8443-1.6755-.9307-.1471-.0273-.2957-.0458-.4451-.0556-.2454 0-.3821-.1215-.3505-.309.0315-.1876.1787-.2223.3154-.2119z"/>
            </g></svg>
        </button>
        
    </div>
</ng-template>
<ng-template #successTpl>{{'PROMOCODE.COPIED' | translate }}</ng-template>
</div> 