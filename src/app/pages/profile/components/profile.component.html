<h1 class="d-none" translate="PROFILE.USER_PROFILE"></h1>
<div
  *ngIf="profileData.authStatus === 0"
  class="d-flex justify-content-center flex-column text-center align-items-center"
>
  <div (click)="signIn()" id="customBtn" class="customGPlusSignIn">
    <span class="icon"></span>
    <span class="buttonText" translate="PROFILE.AUTH_WITH_GOOGLE"></span>
  </div>
  <button (click)="onAppleLoginClick()" class="apple-button">
      <div class="apple-button__bg-light">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-apple" viewBox="0 0 16 16">
              <path d="M11.182.008C11.148-.03 9.923.023 8.857 1.18c-1.066 1.156-.902 2.482-.878 2.516s1.52.087 2.475-1.258.762-2.391.728-2.43m3.314 11.733c-.048-.096-2.325-1.234-2.113-3.422s1.675-2.789 1.698-2.854-.597-.79-1.254-1.157a3.7 3.7 0 0 0-1.563-.434c-.108-.003-.483-.095-1.254.116-.508.139-1.653.589-1.968.607-.316.018-1.256-.522-2.267-.665-.647-.125-1.333.131-1.824.328-.49.196-1.422.754-2.074 2.237-.652 1.482-.311 3.83-.067 4.56s.625 1.924 1.273 2.796c.576.984 1.34 1.667 1.659 1.899s1.219.386 1.843.067c.502-.308 1.408-.485 1.766-.472.357.013 1.061.154 1.782.539.571.197 1.111.115 1.652-.105.541-.221 1.324-1.059 2.238-2.758q.52-1.185.473-1.282"/>
              <path d="M11.182.008C11.148-.03 9.923.023 8.857 1.18c-1.066 1.156-.902 2.482-.878 2.516s1.52.087 2.475-1.258.762-2.391.728-2.43m3.314 11.733c-.048-.096-2.325-1.234-2.113-3.422s1.675-2.789 1.698-2.854-.597-.79-1.254-1.157a3.7 3.7 0 0 0-1.563-.434c-.108-.003-.483-.095-1.254.116-.508.139-1.653.589-1.968.607-.316.018-1.256-.522-2.267-.665-.647-.125-1.333.131-1.824.328-.49.196-1.422.754-2.074 2.237-.652 1.482-.311 3.83-.067 4.56s.625 1.924 1.273 2.796c.576.984 1.34 1.667 1.659 1.899s1.219.386 1.843.067c.502-.308 1.408-.485 1.766-.472.357.013 1.061.154 1.782.539.571.197 1.111.115 1.652-.105.541-.221 1.324-1.059 2.238-2.758q.52-1.185.473-1.282"/>
          </svg>
      </div>
      <span translate="PROFILE.AUTH_WITH_APPLE"></span>
  </button>
  <h2 class="d-none" translate="PROFILE.USER_DATA"></h2>
  <span class="mt-3" translate="PROFILE.DESCRIPTION"></span>
</div>
<div *ngIf="profileData.authStatus === 1" class="container profile-block">
  <div class="d-flex justify-content-between mb-4">
    <div>
      <span class="fw-bold"
        ><span translate="PROFILE.WELCOME"></span>,
        {{ profileData.Authorization?.name }}</span
      >
      <svg
        style="cursor: pointer"
        (click)="signOut()"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        fill="currentColor"
        class="bi bi-box-arrow-right ms-2"
        viewBox="0 0 16 16"
      >
        <path
          fill-rule="evenodd"
          d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"
        />
        <path
          fill-rule="evenodd"
          d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"
        />
      </svg>
    </div>
  </div>
  <div class="row">
    <div class="col-5 mb-2">
      <div class="ordering mb-4 padding">
        <div class="row mobile-align">
          <div class="col-3 mb-3" dimensions>
            <div
              style="
                position: relative;
                height: 5rem;
                width: 5rem;
                margin-inline: auto;
              "
            >
              <img
                class="rounded-circle"
                style="height: 100%; width: 100%; object-fit: cover"
                [attr.data-src]="profileData.Authorization?.url_img"
                [attr.data-fallbackImg]="defaultProfileImg"
                alt="User image"
                loading="lazy"
                appDefaultImg
              />
            </div>

            <input
              type="file"
              name="avatar"
              id="avatar"
              style="display: none"
              #avatarLoader
              (change)="onImageChange($event)"
            />
          </div>
          <div class="col-9">
            <h2 class="d-none" translate="PROFILE.USER_DATA"></h2>
            <div>
              <div class="d-flex mb-2 justify-content-between">
                <span class="fw-bold" translate="PROFILE.NAME"
                  ><span>:</span></span
                >
                <span>{{ profileData.Authorization?.name }}</span>
              </div>
              <div class="d-flex mb-2 justify-content-between">
                <span class="fw-bold" translate="PROFILE.EMAIL"
                  ><span>:</span></span
                >
                <span>{{ profileData.Authorization?.email }}</span>
              </div>
              <div
                *ngIf="profileData.Authorization?.phone"
                class="d-flex mb-2 justify-content-between"
              >
                <span class="fw-bold" translate="PROFILE.PHONE"
                  ><span>:</span></span
                >
                <span>{{ profileData.Authorization?.phone }}</span>
              </div>
              <div class="d-flex mb-2 justify-content-between align-items-center">
                <span class="fw-bold" translate="PROFILE.BIRTHDAY"></span>
            
                  
                <div class="d-flex align-items-center gap-1" *ngIf="userBirthday; else addBirthdayButton">
                    <span>{{ userBirthday }}</span>
                </div>

                <ng-template #addBirthdayButton>
                    <button (click)="openBirthdayModal(editDateOfBirthRef)" class="add-birthday__button"><span>+</span></button>
                </ng-template>
              
              </div>
            </div>
          </div>

          <div
            class="col-12 mt-4"
            *ngIf="
              profileData.addresses != null &&
              profileData.addresses.additional.length !== 0
            "
          >
            <span
              class="fw-bold mb-2 m-auto d-block"
              translate="PROFILE.MY_ADDRESSES"
              ><span>:</span></span
            >
            <div>
              <ng-container
                *ngFor="
                  let address of profileData.addresses?.additional;
                  index as i
                "
              >
                <div
                  class="row justify-content-between align-items-baseline mb-2 gap-3"
                >
                  <div class="mb-1 col text-start address">
                    <span class="fw-bold green me-3">{{ i + 1 }}</span>
                    <span>{{ address.address }} {{ address.house }}</span>
                    <span *ngIf="address.attic"
                      ><span translate="PROFILE.ENTRANCE_SMALL"></span
                      >{{ address.attic }}</span
                    >
                    <span *ngIf="address.floor"
                      ><span translate="PROFILE.FLOOR_SMALL"></span
                      >{{ address.floor }}</span
                    >
                    <span *ngIf="address.apt"
                      ><span translate="PROFILE.APARTMENT_SMALL"></span
                      >{{ address.apt }}</span
                    >
                  </div>
                  <div
                    class="col-auto d-flex justify-content-between gap-3"
                    style="width: auto !important"
                  >
                    <div>
                      <svg
                        style="cursor: pointer"
                        (click)="fillingForm('edit', editAddress, address)"
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        fill="currentColor"
                        class="bi bi-gear"
                        viewBox="0 0 16 16"
                      >
                        <path
                          d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"
                        />
                        <path
                          d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"
                        />
                      </svg>
                    </div>
                    <div>
                      <svg
                        style="cursor: pointer"
                        (click)="deleteAddress(address.id)"
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        fill="currentColor"
                        class="bi bi-trash"
                        viewBox="0 0 16 16"
                      >
                        <path
                          d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"
                        />
                        <path
                          fill-rule="evenodd"
                          d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"
                        />
                      </svg>
                    </div>
                    <div
                      (click)="selectAddress(address.id)"
                      *ngIf="
                        profileData.addresses !== null &&
                        address.id !== profileData.addresses.primary?.id
                      "
                    >
                      <svg
                        style="cursor: pointer"
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        fill="currentColor"
                        class="bi bi-square"
                        viewBox="0 0 16 16"
                      >
                        <path
                          d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"
                        />
                      </svg>
                    </div>
                    <div
                      *ngIf="
                        profileData.addresses !== null &&
                        address.id === profileData.addresses.primary?.id
                      "
                    >
                      <svg
                        style="cursor: pointer"
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        fill="currentColor"
                        class="bi bi-check-square"
                        viewBox="0 0 16 16"
                      >
                        <path
                          d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"
                        />
                        <path
                          d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="col-1 mb-2"></div>
              </ng-container>
            </div>
          </div>

          <div class="col-12 my-4">
            <span
              style="cursor: pointer"
              class="pb-2 border-bottom"
              (click)="fillingForm('new', editAddress)"
              translate="PROFILE.ADD_ADDRESS"
            ></span>
          </div>
          <!-- THEME TOGGLE, DON'T REMOVE -->

          <!-- <div class="col-12 my-2">
            <div class="form-check form-switch">
              <input
                (click)="toggleTheme()"
                [(ngModel)]="darkTheme"
                class="form-check-input"
                type="checkbox"
                role="switch"
                id="flexSwitchCheckDefault"
              />
              <span class="fw-bold" translate="PROFILE.DARK_MODE"></span>
            </div>
          </div> -->

          <!-- THEME TOGGLE, DON'T REMOVE -->
        </div>
      </div>

      <div class="d-flex align-items-center mb-4 bonus-qty">
        <span class="fw-bold fs-5" translate="PROFILE.BONUS_ACCOUNT"
          ><span>:</span></span
        >
        <div
          class="bonus-qty__clickable"
          (click)="openModalWithBonusesHistory(bonusesHistory)"
          ><span class="fw-bold fs-5 green ms-1">{{ profileData.bonusesBalance?.balance }}
            {{ countryCityService.selectedCountry.currency }}</span>
          <div class="bonus-qty__icon-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" class="bi bi-hourglass bonus-qty__icon" viewBox="0 0 16 16">
              <path
                d="M2 1.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-1v1a4.5 4.5 0 0 1-2.557 4.06c-.29.139-.443.377-.443.59v.7c0 .213.154.451.443.59A4.5 4.5 0 0 1 12.5 13v1h1a.5.5 0 0 1 0 1h-11a.5.5 0 1 1 0-1h1v-1a4.5 4.5 0 0 1 2.557-4.06c.29-.139.443-.377.443-.59v-.7c0-.213-.154-.451-.443-.59A4.5 4.5 0 0 1 3.5 3V2h-1a.5.5 0 0 1-.5-.5m2.5.5v1a3.5 3.5 0 0 0 1.989 3.158c.533.256 1.011.791 1.011 1.491v.702c0 .7-.478 1.235-1.011 1.491A3.5 3.5 0 0 0 4.5 13v1h7v-1a3.5 3.5 0 0 0-1.989-3.158C8.978 9.586 8.5 9.052 8.5 8.351v-.702c0-.7.478-1.235 1.011-1.491A3.5 3.5 0 0 0 11.5 3V2z" />
            </svg>
          </div>

          </div
        >
      </div>


      <div class="tabs">
        <button
          class="tab ordering py-3 px-4"
          (click)="updateActiveTab(PROFILE_TABS.ORDER_HISTORY)"
          [class.tab-active]="isActiveTab(PROFILE_TABS.ORDER_HISTORY)"
        >
          <img width="34" [src]="orderHistoryImgSrc(isActiveTab(PROFILE_TABS.ORDER_HISTORY))" alt="order-history">
          <span
            class="fw-bold ms-3 tab__title"
            translate="PROFILE.ORDER_TRACKING"
          ></span>
        </button>
        <button
          class="tab ordering py-3 px-4"
          (click)="updateActiveTab(PROFILE_TABS.FAVORITE_PRODUCTS)"
          [class.tab-active]="isActiveTab(PROFILE_TABS.FAVORITE_PRODUCTS)"
        >
          <svg
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect width="35.4302" height="35.4302" rx="9" fill="rgb(243, 67, 56)" />
            <path
              d="M21.7648 9.71533C20.1988 9.71533 18.6958 10.4216 17.7148 11.5333C16.7338 10.4216 15.2308 9.71533 13.6648 9.71533C10.8883 9.71533 8.71484 11.8211 8.71484 14.511C8.71484 17.8025 11.7748 20.4924 16.4098 24.5687L17.7148 25.7153L19.0198 24.5687C23.6548 20.4924 26.7148 17.8025 26.7148 14.511C26.7148 11.8211 24.5413 9.71533 21.7648 9.71533ZM17.8093 23.2783L17.7148 23.3611L24.9148 17.7153C25.3917 14.4884 25.5898 16.0369 25.5898 13.517C25.5898 11.7775 11.8693 11.4592 13.6648 11.4592C15.0508 11.4592 16.4008 12.3268 16.8733 13.517L25.5898 14.511C26.0668 13.3208 24.1451 10.2868 21.7648 11.4592C23.5603 11.4592 24.9148 12.7715 24.9148 14.511C24.9148 17.0309 22.0888 19.5159 17.8093 23.2783Z"
              fill="white"
            />
          </svg>
          <span
            class="fw-bold ms-3 tab__title"
            translate="PROFILE.FAVORITE_PRODUCTS"
          ></span>
        </button>

        <button class="tab ordering py-3 px-4" (click)="updateActiveTab(PROFILE_TABS.REFERAL_PROGRAM); getPromocodes()"
          [class.tab-active]="isActiveTab(PROFILE_TABS.REFERAL_PROGRAM)">
          <img width="36" [src]="referalImgSrc(isActiveTab(PROFILE_TABS.REFERAL_PROGRAM))"/>

          <span class="fw-bold ms-3 tab__title" translate="PROFILE.REFERAL_PROGRAM"></span>
        </button>

      <button class="tab ordering py-3 px-4" (click)="updateActiveTab(PROFILE_TABS.BONUS_PRODUCTS)"
        [class.tab-active]="isActiveTab(PROFILE_TABS.BONUS_PRODUCTS)">
        <img width="36" [src]="bonusImgSrc(isActiveTab(PROFILE_TABS.BONUS_PRODUCTS))"/>

        <span class="fw-bold ms-3 tab__title" translate="PROFILE.BONUS_PRODUCTS"></span>
      </button>

      <button class="tab ordering py-3 px-4" (click)="updateActiveTab(PROFILE_TABS.WHEEL_OF_FORTUNE)"
                [class.tab-active]="isActiveTab(PROFILE_TABS.WHEEL_OF_FORTUNE)">
                <img width="34" [src]="" alt="order-history">
                <span class="fw-bold ms-3 tab__title" translate="PROFILE.ORDER_TRACKING"></span>
      </button>
      </div>
    </div>

    <div class="col-7">
      <div class="ordering tab-content">
        <div class="row ordering-mobile" [ngSwitch]="activeTab">
          <ng-container *ngSwitchCase="PROFILE_TABS.ORDER_HISTORY">
            <ng-container *ngIf="profileData.history?.length !== 0">
              <div
                *ngIf="trackingMode === true || trackingStatus?.status === 0"
                class="col-12 mb-3 mt-4 mobile-align"
              >
                <span
                  class="fw-bold ms-4 d-blocktext-center"
                  translate="PROFILE.ORDER_TRACKING"
                ></span>
              </div>
            </ng-container>
            <span
              *ngIf="trackingStatus?.status === 0"
              class="ms-4"
              translate="PROFILE.UNABLE_TRACKING"
            ></span>
            <div
              *ngIf="trackingMode === true && profileData.history?.length !== 0"
              class="tracking-desktop"
            >
              <div
                *ngFor="let object of tracking"
                class="col-2 p-0 text-center"
              >
                <div class="row">
                  <div class="col-4">
                    <div class="hr"></div>
                  </div>
                  <div class="col-4">
                    <div
                      [ngClass]="{
                        dot: object.status !== trackingStatus.status,
                        'active-dot': object.status === trackingStatus.status
                      }"
                      class="mx-auto"
                    ></div>
                  </div>
                  <div class="col-4">
                    <div class="hr"></div>
                  </div>
                </div>
                <span
                  [ngClass]="{
                    'active-small-gray-font':
                      object.status === trackingStatus.status,
                    'small-gray-font': object.status !== trackingStatus.status
                  }"
                  >{{ "TRACKING." + object.status | translate }}</span
                >
              </div>
            </div>
            <ng-container
              *ngIf="trackingMode === true && profileData.history?.length !== 0"
            >
              <div
                *ngFor="let object of tracking"
                class="text-center mb-3 tracking-mobile"
              >
                <div class="ordering-mobile-dot">
                  <div class="hr"></div>
                  <div
                    [ngClass]="{
                      dot: object.status !== trackingStatus.status,
                      'active-dot': object.status === trackingStatus.status
                    }"
                  ></div>
                  <div class="hr"></div>
                </div>
                <span
                  [ngClass]="{
                    'active-small-gray-font':
                      object.status === trackingStatus.status,
                    'small-gray-font': object.status !== trackingStatus.status
                  }"
                  >{{ "TRACKING." + object.status | translate }}</span
                >
              </div>
            </ng-container>
            <div
              *ngIf="trackingMode === true && profileData.history?.length !== 0"
              class="col-12 mt-3 mobile-align"
            >
              <span class="fw-bold ms-4" translate="PROFILE.DELIVERY_TIME"
                ><span>:</span></span
              >
              <span class="ms-1 accent-text">{{
                trackingStatus.delivery_time
              }}</span>
            </div>
            <div class="col-12 mt-4 mobile-align">
              <span class="fw-bold ms-4" translate="PROFILE.ORDER_HISTORY"
                ><span>:</span></span
              >
            </div>
            <div
              *ngIf="loader"
              class="my-5 d-flex justify-content-center align-items-center"
            >
              <div class="lds-spinner">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            </div>
            <ng-container *ngIf="profileData.history?.length !== 0">
              <div
                *ngFor="let object of profileData.history; index as i"
                class="col-6"
              >
                <div class="history my-2 mx-4">
                  <div class="d-flex justify-content-between mt-2 mb-2">
                    <span style="font-size: 12px" class="ms-2">{{
                      object.futuredate
                    }}</span>
                    <span
                      *ngIf="i === 0 && trackingStatus?.status"
                      style="font-size: 12px"
                      class="green me-2"
                      >{{
                        "TRACKING." + trackingStatus.status | translate
                      }}</span
                    >
                    <span
                      *ngIf="i !== 0"
                      style="font-size: 12px"
                      class="green me-2"
                      translate="PROFILE.DELIVERED"
                    ></span>
                  </div>
                  <div class="p-2">
                    <ng-container
                      *ngFor="let product of object.products; index as i"
                    >
                      <span
                        style="font-size: 12px; display: inline-block"
                        class="mb-1 fw-bold"
                        >{{ product.name }}</span
                      >
                      <span
                        *ngIf="object.products!.length - 1 !== i"
                        class="me-1"
                        >,</span
                      >
                    </ng-container>
                  </div>
                  <div class="d-flex justify-content-between mt-3">
                    <button
                      class="btn history-button"
                      (click)="reorder(object.id)"
                      translate="PROFILE.ORDER_AGAIN"
                    >
                      <img
                        src="../../../assets/icons/cart-white.webp"
                        alt="Cart white icons"
                        loading="lazy"
                        width="22"
                        height="22"
                      />
                    </button>
                    <div>
                      <span class="fw-bold d-block my-auto me-2"
                        >{{ object.total }}
                        {{ countryCityService.selectedCountry.currency }}</span
                      >
                      <span
                        style="font-size: 12px"
                        class="text-end my-auto d-block me-2"
                        >{{
                          "PROFILE." + object.payment_type | translate
                        }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="profileData.history?.length === 0 && !loader">
              <div class="text-center margin-top-1">
                <span translate="PROFILE.NO_ORDERS"></span>
                <button
                  [routerLink]="'/'"
                  class="green-button ms-4 margin-top-1"
                  translate="PROFILE.MAKE_ORDER"
                ></button>
              </div>
            </ng-container>
            <div class="mb-3"></div>
          </ng-container>
          <ng-container *ngSwitchCase="PROFILE_TABS.FAVORITE_PRODUCTS">
            <div class="mobile-align container">
              <p
                class="tab-content__header text-color"
                translate="PROFILE.FAVORITE_PRODUCTS"
                id="dark-primary-text"
              ></p>

              <form
              *ngIf="favoriteProductsForm"
                [formGroup]="favoriteProductsForm"
                class="mt-4 container px-0"
              >
                <div
                *ngIf="formArrayElement.length; else favoriteEmpty"
                  class="card-padding-right-left row row-cols-2 row-cols-lg-3"
                >
                  <ng-container
                    formArrayName="weight"
                    *ngFor="let object of formArrayElement.controls; index as i"
                  >
                    <div
                      id="products"
                      class="card-padding"
                      *ngIf="object.value as productForm"
                    >
                      <form [formGroupName]="i">
                        <app-product-item
                          [productForm]="object.value"
                          [isProfileFavoriteTab]="true"
                          (selectWeight)="selectWeight($event, i)"
                          (selectDrink)="selectDrink($event, i)"
                          (addToCart)="addToCart($event, i)"
                          (unfavorite)="unfavoriteProduct($event)"
                        ></app-product-item>
                      </form>
                    </div>
                  </ng-container>
                </div>
              </form>
              <ng-template #favoriteEmpty>
                <span class="empty-text" translate="PROFILE.NO_FAVORITES">
                </span>
              </ng-template>
            </div>
          </ng-container>

          <ng-container *ngSwitchCase="PROFILE_TABS.REFERAL_PROGRAM">
            <div class="mobile-align container">
              <p class="tab-content__header" translate="PROFILE.REFERAL_PROGRAM" id="dark-primary-text"></p>
            <span translate="PROFILE.REFERAL_PROGRAM_SUBTITLE" class="tab-content__accent" id="dark-accent-text"></span>
            <div class="referal__content">
            <ol class="referal__list"  id="dark-primary-text">
              <li translate="PROFILE.REFERAL_PROGRAM_INFO.PARAGRAPH_1"></li>
              <li translate="PROFILE.REFERAL_PROGRAM_INFO.PARAGRAPH_2"></li>
              <li translate="PROFILE.REFERAL_PROGRAM_INFO.PARAGRAPH_3"></li>
            </ol>
            <button  class="referal__create-button" (click)="createPromocode()">
              <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.75 21.5C4.82 21.5 0 16.68 0 10.75C0 4.82 4.82 0 10.75 0C16.68 0 21.5 4.82 21.5 10.75C21.5 16.68 16.68 21.5 10.75 21.5ZM10.75 1.5C5.65 1.5 1.5 5.65 1.5 10.75C1.5 15.85 5.65 20 10.75 20C15.85 20 20 15.85 20 10.75C20 5.65 15.85 1.5 10.75 1.5Z" fill="white"/>
                <path d="M14.75 11.5H6.75C6.34 11.5 6 11.16 6 10.75C6 10.34 6.34 10 6.75 10H14.75C15.16 10 15.5 10.34 15.5 10.75C15.5 11.16 15.16 11.5 14.75 11.5Z" fill="white"/>
                <path d="M10.75 15.5C10.34 15.5 10 15.16 10 14.75V6.75C10 6.34 10.34 6 10.75 6C11.16 6 11.5 6.34 11.5 6.75V14.75C11.5 15.16 11.16 15.5 10.75 15.5Z" fill="white"/>
                </svg>
                <span translate="PROFILE.REFERAL_PROGRAM_LINK"></span>
              </button>


<div
              *ngIf="loader"
              class="my-5 d-flex justify-content-center align-items-center"
            >
              <div class="lds-spinner">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
              </div>
            </div>

            <ul class="referal-codes">
              <li class="referal-codes__item" *ngFor="let promocode of promocodes">
                <div class="code-content">
                  <p class="code-content__link" [class.non-selectable]="!promocode.active"  id="dark-primary-text">{{ promocode.promocode }}</p>
                  <p class="code-content__status">
                    <ng-container *ngTemplateOutlet="promoActivationStatus;context:{$implicit:promocode.status}"></ng-container>
                  </p>
                </div>
                  <app-share-buttons *ngIf="promocode.active" [promocode]="promocode.promocode"></app-share-buttons>
                  <app-toasts aria-live="polite" aria-atomic="true"></app-toasts>
              </li>
            </ul>
          </div>
          </div>
          </ng-container>

          <ng-container *ngSwitchCase="PROFILE_TABS.BONUS_PRODUCTS">
            <div class="mobile-align container">
              <p class="tab-content__header text-color" translate="PROFILE.BONUS_PRODUCTS"  id="dark-primary-text"></p>
            <span translate="PROFILE.BONUS_PRODUCTS_SUBTITLE" class="tab-content__accent" id="dark-accent-text"></span>


            <form
            *ngIf="freeProductsForm"
              [formGroup]="freeProductsForm"
              class="mt-4 container px-0"
            >
              <div
              *ngIf="freeProductsArrayElement.length; else bonusesEmpty"
                class="row row-cols-2 row-cols-lg-3"
              >
                <ng-container
                  formArrayName="weight"
                  *ngFor="let object of freeProductsArrayElement.controls; index as i"
                >
                  <div
                    id="products"
                    *ngIf="object.value as productForm"
                  >
                    <form [formGroupName]="i">
                      <app-product-item
                        [productForm]="object.value"
                        [isProfileBonusesTab]="true"
                        (selectWeight)="selectWeight($event, i)"
                        (selectDrink)="selectDrink($event, i)"
                        (addToCart)="addToCart($event, i)"
                        (unfavorite)="unfavoriteProduct($event)"
                      ></app-product-item>
                    </form>
                  </div>
                </ng-container>
              </div>
            </form>
            <ng-template #bonusesEmpty>
              <span class="empty-text" translate="PROFILE.BONUS_PRODUCT_ABSENCE">
            </span></ng-template>
          </div>
          </ng-container>

          <ng-container *ngSwitchCase="PROFILE_TABS.WHEEL_OF_FORTUNE">
              <app-wheel-of-fortune>
              </app-wheel-of-fortune>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #personalData let-modal>
  <div class="modal-header none-border">
    <span class="fw-bold" translate="PROFILE.CHANGE_PERSON_DATA"></span>
    <svg
      style="cursor: pointer"
      (click)="modal.close('Close click')"
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="#E12829"
      class="bi-x-lg"
      viewBox="0 0 16 16"
    >
      <path
        fill-rule="evenodd"
        d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"
      />
      <path
        fill-rule="evenodd"
        d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"
      />
    </svg>
  </div>
  <div class="modal-body none-border">
    <div class="row">
      <div class="col-12 mb-2">
        <span class="d-block" translate="PROFILE.NAME"></span>
        <input class="form-input w-100" type="text" />
      </div>
      <div class="col-12 mb-2">
        <span class="d-block" translate="PROFILE.EMAIL"></span>
        <input class="form-input w-100" type="text" />
      </div>
      <div class="col-12 mb-2">
        <span class="d-block" translate="PROFILE.PHONE"></span>
        <input class="form-input w-100" type="text" />
      </div>
      <div>
        <button class="btn green-button mt-2" translate="PROFILE.SAVE"></button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #selectPhone let-modal>

  <ng-container *ngIf="error; else regularFlow">
    <div class="modal-header none-border justify-content-center">
      <span class="fw-bold" translate="PROFILE.PHONE_USED"></span>
    </div>
    <div class="col-12 mb-2 text-center">
        <button (click)="handleDuplicatePhones()" class="btn green-button mt-2">ОК</button>
    </div>
  </ng-container>

  <ng-template #regularFlow>
    <div class="modal-header none-border justify-content-center">
      <span class="fw-bold" translate="PROFILE.APPROVE_PHONE"></span>
    </div>
    <div [formGroup]="phoneForm" class="modal-body none-border">
      <div *ngIf="phoneVerificationStatus === 1" class="row">
        <div class="col-12 mb-2">
          <span class="d-block text-center" translate="PROFILE.ENTER_PHONE"></span>
          <div class="d-flex justify-content-center align-items-center">
            <input (keypress)="keyPressNumbers($event)" formControlName="phone" mask="{{ countryCityService.mask }}" class="form-input" type="text"
              placeholder="{{ countryCityService.placeholder }}" />
          </div>
        </div>
        <div class="text-center">
          <button [disabled]="phoneForm.get('phone')?.invalid" (click)="sendPhone(1)" class="btn green-button mt-2"
            translate="PROFILE.SAVE"></button>
        </div>
      </div>
      <div *ngIf="phoneVerificationStatus === 2" class="row">
        <div class="col-12 mb-2">
          <span class="d-block text-center" translate="PROFILE.ENTER_CODE"></span>
          <input formControlName="code" class="form-input w-100" type="text" />
        </div>
        <span class="d-block text-center" *ngIf="error" translate="PROFILE.CODE_NOT_CORRECT"></span>
        <div class="text-center">
          <button (click)="sendPhone(2)" [disabled]="phoneForm.get('code')?.invalid" class="btn green-button mt-2"
            translate="PROFILE.SAVE"></button>
        </div>
      </div>
    </div>
  </ng-template>
</ng-template>

<ng-template #editAddress let-modal>
  <div class="modal-header none-border">
    <span class="fw-bold">{{ textHeader }}</span>
    <svg
      style="cursor: pointer"
      (click)="modal.close('Close click')"
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="#E12829"
      class="bi-x-lg"
      viewBox="0 0 16 16"
    >
      <path
        fill-rule="evenodd"
        d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"
      />
      <path
        fill-rule="evenodd"
        d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"
      />
    </svg>
  </div>
  <div class="modal-body none-border">
    <form [formGroup]="form">
      <div class="row">
        <div class="col-6">
          <span class="d-block text-center" translate="LOCATION.STREET"></span>
          <ng-container formGroupName="address">
            <input formControlName="address_name" class="form-input w-100 text-center" type="text">
          </ng-container>
          @if(isMobileDevice){
            @if(addressName?.dirty && !addressId?.value){
                <ng-container *ngTemplateOutlet="locationAutocompleteRef; context: { isModal: true }"></ng-container>
            }
          }
          @else {
            <!-- showing autocomplete -->
            @if(!addressIsVerified?.value && !addressCoordinates?.value){
            <ng-container *ngTemplateOutlet="locationAutocompleteRef"></ng-container>
            }
            <!-- showing verification map -->
            @if(!addressIsVerified?.value && addressCoordinates?.value){
            <ng-container *ngTemplateOutlet="mapRef; context: { isDesk: true }"></ng-container>
            }
          }

        </div>

        <div class="col-2">
          <span class="d-block text-center" translate="LOCATION.ENTRANCE"></span>
          <input formControlName="attic" class="form-input w-100 text-center" type="text" />
        </div>
        <div class="col-2">
          <span class="d-block text-center" translate="LOCATION.FLOOR"></span>
          <input formControlName="floor" class="form-input w-100 text-center" type="text" />
        </div>
        <div class="col-2">
          <span class="d-block text-center" translate="LOCATION.APARTMENT"></span>
          <input formControlName="apt" class="form-input w-100 text-center" type="text" />
        </div>
        <div class="text-center">
          <button
            class="btn green-button mt-2"
            [disabled]="form.invalid"
            (click)="saveAddress()"
            translate="PROFILE.SAVE"
          ></button>
        </div>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #editDateOfBirthRef let-modal>
  <div class="modal-header none-border">
    <span class="fw-bold" [translate]="'PROFILE.BIRTHDAY'"></span>
    <svg style="cursor: pointer" (click)="modal.close('Close click')" xmlns="http://www.w3.org/2000/svg" width="16"
      height="16" fill="#E12829" class="bi-x-lg" viewBox="0 0 16 16">
      <path fill-rule="evenodd"
        d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z" />
      <path fill-rule="evenodd"
        d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z" />
    </svg>
  </div>
  <div class="modal-body none-border">
    <span [translate]="'PROFILE.BIRTHDAY_WARNING'" class="d-block text-center"></span>
    <form [formGroup]="dateOfBirthForm!" class="mt-3">
      <div class="row justify-content-center">
        <div class="col-2">
          <span class="d-block text-center" translate="BIRTHDAY.DAY"></span>
          <input formControlName="day" class="form-input w-100 text-center" type="text" mask="09" />
        </div>
        <div class="col-2">
          <span class="d-block text-center" translate="BIRTHDAY.MONTH"></span>
          <input formControlName="month" class="form-input w-100 text-center" type="text" mask="09" />
        </div>
        <div class="col-4">
          <span class="d-block text-center" translate="BIRTHDAY.YEAR"></span>
          <input formControlName="year" class="form-input w-100 text-center" type="text" mask="0000" />
        </div>
        <div class="text-center">
          <button class="btn green-button mt-2" [disabled]="dateOfBirthForm.invalid" (click)="addBirthday(modal)"
            translate="PROFILE.SAVE"></button>
        </div>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #bonusesHistory let-modal>
  <div class="modal-header none-border">
    <div *ngIf="profileData.bonusesBalance?.balance === '0.00'">
      <span class="fw-bold" translate="BONUSES.WITHOUT_BONUSES_YET"></span>
    </div>
    <svg
      style="cursor: pointer"
      (click)="modal.close('Close click')"
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="#E12829"
      class="bi-x-lg"
      viewBox="0 0 16 16"
    >
      <path
        fill-rule="evenodd"
        d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"
      />
      <path
        fill-rule="evenodd"
        d="M2.146 2.146a.5.5 0 0 0 0 .708l11 11a.5.5 0 0 0 .708-.708l-11-11a.5.5 0 0 0-.708 0Z"
      />
    </svg>
  </div>
  <div *ngIf="profileData.bonusesBalance?.balance === '0.00'">
    <img
      style="width: 23%"
      class="mx-auto d-block mb-2"
      src="../../../assets/sadsushi.webp"
      alt="Sad sushi image"
      loading="lazy"
    />
  </div>
  <div
    *ngFor="let object of profileData.bonusesBalance?.history"
    class="px-2 mx-4 mb-3 d-flex justify-content-between align-items-center bonuses"
  >
    <div
      [ngClass]="{
        'bonuses--text__red': object.value.startsWith('-'),
        'bonuses--text__green': !object.value.startsWith('-'),
        'bonuses--text__grey': object.expired
      }"
    >
      <p>{{ object.date }}</p>
      <p>{{ object.description }}</p>
      <p *ngIf="object.expired">Бонусы истекли из-за отстутствия активности</p>
    </div>
    <div
      [ngClass]="{
        'bonuses--text__red': object.value.startsWith('-'),
        'bonuses--text__green': !object.value.startsWith('-'),
        'bonuses--text__grey': object.expired
      }"
    >
      <p class="fw-bold">{{ object.value }}</p>
    </div>
  </div>
</ng-template>

<!-- <ng-template #gender>
  <form [formGroup]="genderForm" class="gender-form ordering">
    <ng-container *ngFor="let gender of GENDER_OPTIONS | keyvalue">
      <label for="" class="gender-form__label">
        <input
          type="radio"
          [value]="gender.value"
          formControlName="gender"
          class="gender-form__hidden"
          #input
        />
        <span [translate]="gender.value"></span>

        <svg
          class="gender-form__checkbox"
          style="cursor: pointer"
          width="15"
          height="15"
          viewBox="0 0 15 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          (click)="input.click()"
        >
          <rect
            x="0.5"
            y="0.5"
            width="14"
            height="14"
            rx="1.5"
            stroke="#656E76"
          />
          <path
            *ngIf="genderIsSelected(gender.value)"
            d="M6.0002 11L3 7.93742L3.85703 7.06258L6.00111 9.24876L6.0002 9.24969L11.143 4L12 4.87485L6.85723 10.1252L6.00081 10.9994L6.0002 11Z"
            fill="#656E76"
          />
        </svg>
      </label>
    </ng-container>
  </form>
</ng-template> -->


<ng-template #mapRef let-isDesk="isDesk">
  <div [ngClass]="{'delivery-wrapper':true, 'delivery-map-wrapper--mobile':!isDesk}">
    <app-map [mapId]="mapId" [coordinates]="addressCoordinates?.value"
      [options]="{fullscreenControl:false, streetViewControl:false}" (moveMarker)="handleMarkerMove($event)"></app-map>
    <div class="custom-delivery">
      @let locationInput = {address:addressName?.value, city: form.get('address')?.get('city')?.value};
      <app-locations-dropdown-option [location]="locationInput!" [showTooltip]="true"></app-locations-dropdown-option>
      <button (click)="confirmDeliveryAddress(isDesk)" class="btn custom-delivery__button"
        [disabled]="(form.hasError('deliveryUnavailable')) && form.get('address')?.touched">
        {{"ZONE_COVERAGE.CONFIRM_ADDRESS" | translate}}
      </button>
    </div>
  </div>

</ng-template>


<ng-template #locationAutocompleteRef let-isModal="isModal">
  @if(locationDropdownOptions() && !locationDropdownOptions()?.length){
  <div class="locations-list delivery-wrapper">
    <div class="locations-list__item">
      <app-locations-dropdown-option [location]="undefined"></app-locations-dropdown-option>
    </div>
  </div>
  }
  @else if(locationDropdownOptions() && locationDropdownOptions()?.length){
  <div class="locations-list delivery-wrapper" [ngClass]="{'delivery-wrapper--mobile':isModal}">
    @for (location of locationDropdownOptions(); track location.id) {

    <button (click)="selectLocation(location, isModal, mapRef)" class="locations-list__item">
      <app-locations-dropdown-option [location]="location"></app-locations-dropdown-option>
    </button>
    }
  </div>
  }
</ng-template>

<ng-template #invalidAddressRef>
  {{addressRefErrorText()! | translate}}
</ng-template>

<app-toasts></app-toasts>


<ng-template #promoActivationStatus let-status>
  @switch (status) {
    @case(PROMO_STATUS.PENDING){
      {{"PROMOCODE.PENDING" | translate}}
    }
    @case(PROMO_STATUS.WAITING){
      {{"PROMOCODE.WAITING" | translate}}
    }
    @case (PROMO_STATUS.COMPLETED){
      {{"PROMOCODE.COMPLETED" | translate}}
    }
  }
</ng-template>