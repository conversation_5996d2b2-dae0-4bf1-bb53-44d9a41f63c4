import {
  ChangeDetectorRef,
  Component,
  effect,
  ElementRef,
  inject,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ProfileRestService } from '../services/profile-rest.service';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  debounceTime,
  EMPTY,
  filter, from, Observable,
  of,
  pluck,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap, withLatestFrom
} from 'rxjs';
import { Addresses } from '../interfaces/profile.interface';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { TrackingInterface } from '../interfaces/tracking.interface';
import { Promocode, PromoStatus } from '../interfaces/promocode.interface';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from '../../../unsubscribe';
import { StorageLogicService } from '../../../shared/utils/services/storage-logic.service';
import { ProfileDataservice } from '../services/profile-data.service';
import { CountryCityNotificationService } from 'src/app/shared/utils/services/country-city-notification.service';
import { CartLogicService } from 'src/app/shell/components/navbar/cart/services/cart-logic.service';
import { ThemeLogicService } from '../../../shared/theme/services/theme-logic.service';
import { ProfileTabs } from '../enums/profile-tabs.enum';
import { ClickOutsideDirective } from 'src/app/shared/directives/click-outside.directive';
import { Prices, ProductData } from '../../home/<USER>/home.interface';
import { ProductDataAdapter } from 'src/app/shared/utils/poduct-data-adapter';
import { GoogleAnalyticsLogicService } from 'src/app/shared/analytics/service/GoogleAnalytics-logic.service';
import { DefaultImgDirective } from 'src/app/shared/directives/default-img.directive';
import { FavoriteProductsApiService } from 'src/app/shared/utils/services/favorite-products-api.service';
import { ActivatedRoute, Router } from '@angular/router';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { FreeProductModalComponent } from './free-product-modal/free-product-modal.component';
import { FormMode } from '../enums/form-mode.enum';
import { ReCaptchaV3Service } from 'ng-recaptcha-2';
import { DeliveryLocation, DeliveryLocationZoneCoverage } from 'src/app/shared/interfaces/delivery-location.interface';
import { LocationApiService } from 'src/app/shared/utils/services/location-api.service';
import { ToastService } from 'src/app/shared/utils/services/toast.service';
import { WheelOfFortuneService } from 'src/app/shared/wheel-of-fortune/services/wheel-of-fortune.service';
import { WheelAction, WheelSegment } from 'src/app/shared/wheel-of-fortune/models/wheel-of-fortune.model';
import { appleConfig } from 'src/environments/apple-config.example';

declare const AppleID: any;

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  providers: [ClickOutsideDirective, DefaultImgDirective],
  animations: [
    trigger('slideInOut', [
      state(
        'void',
        style({
          transform: 'translateX(100%)',
          opacity: 0,
        })
      ),
      state(
        '*',
        style({
          transform: 'translateX(0)',
          opacity: 1,
        })
      ),
      transition(':enter', [animate('250ms ease-in')]),
      transition(':leave', [
        animate(
          '250ms ease-out',
          style({
            transform: 'translateX(100%)',
            opacity: 0,
          })
        ),
      ]),
    ]),
  ],
})
export class ProfileComponent
  extends Unsubscribe
  implements OnInit
{
  trackingStatus!: TrackingInterface;
  loader = false;
  textHeader = '';
  dateOfBirthTextHeader = 'PROFILE.ADD_BIRTHDATE'
  darkTheme = this.themeLogicService.isDarkTheme;


  trackingMode = false;
  error = false;
  phoneVerificationStatus = 0;

  form!: FormGroup;
  phoneForm!: FormGroup;
  // genderForm!: FormGroup;
  favoriteProductsForm!: FormGroup;
  freeProductsForm!: FormGroup;
  dateOfBirthForm = this.fb.group({
    day: ['', [Validators.required,Validators.pattern(/^(0?[1-9]|[12][0-9]|3[01])$/)]],
    month: ['', [Validators.required,Validators.pattern(/^(0?[1-9]|1[0-2])$/)]],
    year: ['', [Validators.required,Validators.pattern(/^\d{4}$/)]]
  })

  tracking = [
    { status: 1 },
    { status: 2 },
    { status: 3 },
    { status: 4 },
    { status: 5 },
    { status: 6 },
  ];

  activeTab: ProfileTabs = ProfileTabs.ORDER_HISTORY;

  PROFILE_TABS = ProfileTabs;

  FORM_MODE = FormMode;

  defaultProfileImg = 'assets/avatar-default.png';

  favoriteProducts: ProductData[] = [];

  @ViewChild('selectPhone', { static: true }) selectPhone!: ElementRef;

  private unfavoriteStream = new BehaviorSubject<number | undefined>(undefined);

  public activeStatus! : string;

  promocodes : Promocode[] = [];

  showShareButtons = false;

  isMobileDevice = false;

  userBirthday: string | null = null;

  @ViewChild('invalidAddressRef') addressIsRouteErrorRef!: TemplateRef<any>;

  @ViewChild('mapRef') mapRef!: TemplateRef<any>;

  private readonly locationApiService = inject(LocationApiService);

  private readonly toastService = inject(ToastService);

  public mapId: string | null = null;

  public addressRefErrorText = signal<string | null>(null);

    public locationDropdownOptions = signal<DeliveryLocation[] | null>(null);

    public selectedDeliveryLocation = signal<DeliveryLocation | null>(null);

  openMapModalRef?: NgbModalRef;

  wheelOfFortuneConfig = this.wheelService.currentWheelConfig;

  wheelTrophy = this.wheelService.currentWheelTrophy;

  protected readonly WHEEL_BUTTON_ACTION = WheelAction;

  initiateSpin = signal<boolean>(false);

  protected readonly PROMO_STATUS = PromoStatus;

  constructor(
    private modalService: NgbModal,
    private service: ProfileRestService,
    public translate: TranslateService,
    public profileData: ProfileDataservice,
    private fb: FormBuilder,
    public storageService: StorageLogicService,
    public countryCityService: CountryCityNotificationService,
    private cartLogic: CartLogicService,
    private themeLogicService: ThemeLogicService,
    private productDataAdapter: ProductDataAdapter,
    private GAService: GoogleAnalyticsLogicService,
    private cartLogicService: CartLogicService,
    private favoriteService: FavoriteProductsApiService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private cdRef: ChangeDetectorRef,
    private readonly recaptchaV3Service: ReCaptchaV3Service,
    private readonly profileRestService: ProfileRestService,
    private readonly wheelService: WheelOfFortuneService
  ) {
    super();

    effect(() => {
      setTimeout(() => {
        this.initiateSpin.set(true)
      },1000)
    })
  }

  ngOnInit(): void {
    this.initGoogleMapId();

    this.handlePhonelessUser();

    this.identifyActiveTabAfterNavigation();

    this.initForms();

    this.updateFavoriteProducts();

    if (this.profileData.history && this.profileData.history.length > 0) {
      this.getTracking(this.profileData.history[0].id);
    }

    this.handlePromo();

    this.userBirthday = this.profileData.Authorization?.birthday ?? null;

    this.observeAddressCtrl();

    this.checkDeviceType();

    this.initAppleSignIn();
  }

  handlePhonelessUser() {
    this.profileData.authDataStream
      .pipe(
        filter(()=>this.phoneVerificationStatus === 0),
        takeUntil(this.$destroy))
      .subscribe((data) => {
        this.phoneVerificationStatus = 1;
        if (data && !data.phone) {
          
          this.openModal(this.selectPhone, 'sm');
      }
      })
  }

  handlePromo(){
    this.profileData.authPhoneStatus.pipe(
      filter(Boolean),
      withLatestFrom(this.activatedRoute.queryParamMap),
      switchMap(([authPhoneStatus, paramMap]) => {
        this.updateFreeProducts();
        const promocode = paramMap.get('c');
        if (promocode && this.profileData?.Authorization?.phone) {
          return this.service.getFreeProductPreview(promocode).pipe(
            tap((res) => {
              if (res.success) {
                this.showFreeProductModal(res.data?.product!);
                this.storageService.removeSessionData('promocode');
                this.router.navigate([], {
                  queryParams: { c: null },
                  queryParamsHandling: 'merge',
                });
              }
            })
          );
        }
        return of();
      }),
    ).subscribe();
    this.cdRef.detectChanges();
  }

  showFreeProductModal(data: ProductData, size = 'lg') {
    const modalRef = this.modalService.open(FreeProductModalComponent, {
      size,
      keyboard: false,
      backdrop: 'static',
    });
    modalRef.componentInstance.product = data;
    modalRef.componentInstance.userName = this.profileData.Authorization?.name;

    modalRef.dismissed.pipe(takeUntil(this.$destroy)).subscribe(
      () => {
        this.resetPromoParam();
      }
    )
  }

  private resetPromoParam(): void {
    this.router.navigate([], {
        queryParams: { c: null },
        queryParamsHandling: 'merge',
    });
  }

  initForms(): void {
    this.form = this.fb.group({
      id: [],
      address: this.fb.group({
        address_name: [null, [Validators.required]],
        address_id: [null],
        coordinates: [null, [Validators.required]],
        city: [null],
        is_verified: [false],
        is_available: [false],
      }),
      attic: [''],
      floor: ['', [Validators.pattern('^[0-9]+$')]],
      apt: ['', [Validators.pattern('^[0-9]+$')]],
    });

    this.phoneForm = this.fb.group({
      phone: [
        '',
        [
          Validators.required,
          Validators.pattern('^[0-9]+$'),
          Validators.minLength(
            this.countryCityService.selectedCountry.phone_length +
              this.countryCityService.selectedCountry.phone_code.length -
              1
          ),
          Validators.maxLength(
            this.countryCityService.selectedCountry.phone_length +
              this.countryCityService.selectedCountry.phone_code.length -
              1
          ),
        ],
      ],
      code: [
        '',
        [
          Validators.required,
          Validators.pattern('^[0-9]+$'),
          Validators.minLength(6),
          Validators.maxLength(6),
        ],
      ],
    });
  }

    get addressIsVerified(): FormControl | null {
      return this.form.get('address')?.get('is_verified') as FormControl | null;
    }

    get addressName(): FormControl | null {
      return this.form.get('address')?.get('address_name') as FormControl | null;
    }

    get addressId(): FormControl | null {
      return this.form.get('address')?.get('address_id') as FormControl | null;
    }

    get addressCoordinates(): FormControl | null {
      return this.form.get('address')?.get('coordinates') as FormControl | null;
    }

  reorder(quicksaleId: number): void {
    this.cartLogic.orderAgain(quicksaleId);
  }

  getTracking(id: number): void {
    this.service
      .tracking(id)
      .pipe(
        tap((trackigData) => {
          this.trackingStatus = trackigData;
          if (
            this.trackingStatus.status !== 6 &&
            this.trackingStatus.status !== 0
          ) {
            this.trackingMode = true;
          }
        })
      )
      .subscribe();
  }

  signIn() {
    this.loader = true;
    this.verifyRecaptcha()
      .pipe(
          switchMap(token=>this.profileData.signIn(token)),
          tap(() => {
            if (
              this.profileData.Authorization &&
              !this.profileData.Authorization?.phone
            ) {
              this.phoneVerificationStatus = 1;
                this.openModal(this.selectPhone, 'sm');
              }
              this.loader = false;
            })
      )
      .subscribe();
  }

  signOut(): void {
    this.profileData.signOut();
  }

  fillingForm(mode: string, content: any, address?: Addresses): void {
    switch (mode) {
      case 'edit':
        this.translate
          .get('PROFILE.CHANGE_ADDRESS')
          .subscribe((name) => (this.textHeader = name));
        if (address) {
          this.form.patchValue({
            id: address.id,
            attic: address.attic,
            floor: address.floor,
            apt: address.apt
          })
          /* Show confirmation modal if address is obsolete, aka doesn't have coordinates */
          const isAddressVerified = !!address.longitude && !!address.latitude;
          if (!isAddressVerified) {
            this.handleUnverifiedAddress(address);
          } else {
            this.addressCoordinates?.setValue({ lat: Number(address?.latitude), lng: Number(address.longitude) })
          }
          this.form.get('address')?.patchValue({ address_name: address.address, is_verified: isAddressVerified });
        }
        break;

      case 'new':
        this.translate
          .get('PROFILE.ADD_ADDRESS')
          .subscribe((name) => (this.textHeader = name));
        this.form.reset();
        break;
    }
    this.openModal(content);

    /* OPEN MAP MODAL FOR MOBILE DEVICES IF ADDRESS UNVERIFIED */
    if (address && (!address.longitude && !address.latitude) && this.isMobileDevice) {
      this.openDeliveryConfirmationModal(this.mapRef);
    }
  }

  handleUnverifiedAddress(primaryAddress: Addresses) {
    this.locationApiService.getUnverifiedAddressCoordinates(primaryAddress.id).subscribe(res => {
      if (res.success) {
        this.addressCoordinates?.setValue(res.data?.coordinates);
        this.addressId?.setValue(res.data?.place_id);
      }
    })
  }

  openBirthdayModal(template:any) {
    this.dateOfBirthForm.reset();
    this.openModal(template, 'md');
  }

  addBirthday(modalRef:NgbModalRef) {
    const { day, month, year } = this.dateOfBirthForm.getRawValue();
    const birthday = `${year}-${month?.padStart(2, '0')}-${day?.padStart(2, '0')}`;

    this.service.addUserBirthday(birthday)
      .pipe(
        catchError(() => {
          modalRef.close();
          return EMPTY;
        }),
        filter(res => res.success),
        switchMap(() => this.service.getInfo()),
        tap(res => this.userBirthday = res.data.birthday ?? null),
        takeUntil(this.$destroy))
      .subscribe(() => modalRef.close());
  }

  toggleTheme() {
    this.themeLogicService.toggleDarkTheme();
  }

  saveAddress(): void {
    let edit = '';
    let create = '';

    this.translate
      .get('PROFILE.CHANGE_ADDRESS')
      .subscribe((name) => (edit = name));
    this.translate
      .get('PROFILE.ADD_ADDRESS')
      .subscribe((name) => (create = name));

    const formattedFormData = this.formatFormDataForSubmission();

    if (this.textHeader === edit) {
      this.editAddress(formattedFormData);
    } else if (this.textHeader === create) {
      this.submitAddAddressForm(formattedFormData);
    }
  }

  private formatFormDataForSubmission() {
    const { address, ...formData } = this.form.value;

    const address_id = formData.id;

    const formattedAddress = {
      ...address_id && {address_id},
      address: address.address_name.trim(),
      latitude: address.coordinates.lat,
      longitude: address.coordinates.lng
    }

    const formattedFormData = { ...formData, ...formattedAddress };
    return formattedFormData;
  }

  submitAddAddressForm(formattedData:Addresses): void {
    this.profileData
      .addAddress(formattedData)
      .subscribe({ complete: () => this.modalService.dismissAll() });
  }

  deleteAddress(id: number): void {
    this.profileData.deleteAddress(id);
  }

  editAddress(formattedData:Addresses): void {
    this.profileData
      .editAddress(formattedData)
      .subscribe({ complete: () => this.modalService.dismissAll() });
  }

  selectAddress(id: number): void {
    this.profileData.selectAddress(id);
  }

  openModal(content: any, size?: string) {
    let sizeModal = 'lg';
    if (size) {
      sizeModal = size;
    }
    this.modalService.open(content, {
      size: sizeModal,
      keyboard: false,
      backdrop: 'static',
    });
  }

  sendPhone(state: number): void {
    let body: {};
    if (state === 1) {
      body = {
        phone: this.phoneForm.get('phone')?.value,
      };
    } else {
      body = {
        phone: this.phoneForm.get('phone')?.value,
        code: this.phoneForm.get('code')?.value,
      };
    }
    this.verifyRecaptcha()
      .pipe(
        switchMap(recaptchaToken => this.service.sendPhone({ ...body, recaptchaToken })),
        takeUntil(this.$destroy)
    )
      .subscribe({
        next: () => {
           if (state === 1) {
            this.phoneVerificationStatus = 2;
          } else {
            this.profileData.Authorization!.phone =
              this.phoneForm.get('phone')?.value;
            this.storageService.setData(
              'Authorization',
              this.profileData.Authorization
            );
            this.profileData.updatePhoneVerificationStatus(true)
            this.modalService.dismissAll();
          }
        },
        error: (value) => {
          if (value) {
            this.error = true;
          }
          this.profileData.clearAuthDataStream();
        },
      });
  }

  openModalWithBonusesHistory(content: any): void {
    this.modalService.open(content);
  }

  updateActiveTab(tab: ProfileTabs) {
    this.activeTab = tab;
  }

  isActiveTab(tab: ProfileTabs) {
    return tab === this.activeTab;
  }

  onImageChange(event: any): void {
    const file = event.target.files[0];

    if (file) {
      const reader = new FileReader();

      reader.onload = (e: any) => {
        this.defaultProfileImg = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  private initFavoriteProductsForm() {
    return this.favoriteService.getFavoriteProducts().pipe(
      pluck('data'),
      tap((favProducts) => {
        this.favoriteProductsForm = this.fb.group({
          weight: this.fb.array(
            favProducts.map((item: ProductData) =>
              this.generateFormGroupElement(item)
            )
          ),
        });
      })
    );
  }

  private initFreeProductsForm() {
    return this.service.getFreeProducts().pipe(
      pluck('data'),
      tap((freeProducts) => {
        this.freeProductsForm = this.fb.group({
          weight: this.fb.array(
            freeProducts.map((item: ProductData) =>
              this.generateFormGroupElement(item)
            )
          ),
        });
      })
    );
  }

  generateFormGroupElement(item: ProductData): FormGroup {
    return this.productDataAdapter.adaptProductData(item);
  }

  get formArrayElement(): FormArray {
    return this.favoriteProductsForm?.get('weight') as FormArray;
  }

  get freeProductsArrayElement():FormArray {
    return this.freeProductsForm.get('weight') as FormArray
  }

  selectDrink(prices: Prices, i: number) {
    const { id, weight, price, img, regular_price, sale_percent } = prices;
    const weightItem = this.favoriteProductsForm.value.weight[i];

    weightItem.weight = id;
    weightItem.weightText = weight;
    weightItem.price = price;
    weightItem.regular_price = regular_price;
    weightItem.sale_percent = sale_percent;
    weightItem.img = img;
  }

  selectWeight(prices: Prices, i: number) {
    const item = this.favoriteProductsForm.value.weight[i];
    item.price = prices.price;
    item.weight = prices.id;
    item.regular_price = prices.regular_price;
    item.sale_percent = prices.sale_percent;
  }

  addToCart(id: number, i: number): void {
    const product = this.favoriteProducts[i]?.prices?.find(
      (product: Prices) => product.id === id
    );
    if (product) {
      this.GAService.eventEmitter('add_to_cart', {
        items: [
          {
            item_name: this.favoriteProducts[i].name,
            item_id: product.id,
            price: product.price,
            quantity: 1,
          },
        ],
      });
    }

    this.cartLogicService.addProduct(id);
  }

  unfavoriteProduct(product: ProductData): void {
    this.unfavoriteStream.next(product.id);
  }

  updateFavoriteProducts(): void {
    combineLatest([
      this.profileData.isAuthorizedStream,
      this.unfavoriteStream,
      this.countryCityService.languageChanged$.pipe(startWith('')),
    ])
      .pipe(
        filter(([isAuth]) => !!isAuth),
        switchMap(([isAuth, id, lang]) =>
          id
            ? this.favoriteService
                .unfavoriteProduct(id)
                .pipe(switchMap(() => this.initFavoriteProductsForm()))
            : this.initFavoriteProductsForm()
        ),
        takeUntil(this.$destroy)
      )
      .subscribe();
  }

  updateFreeProducts() {
    combineLatest([
      this.profileData.isAuthorizedStream,
      this.countryCityService.languageChanged$.pipe(startWith('')),
    ])
      .pipe(
        filter(([isAuth]) => !!isAuth),
        switchMap(() => this.initFreeProductsForm()),
        takeUntil(this.$destroy)
      ).subscribe();
  }

  identifyActiveTabAfterNavigation(): void  {
    this.activatedRoute.queryParamMap
      .pipe(
        tap((query) => {
          if (query.get('tab') === ProfileTabs.FAVORITE_PRODUCTS) {
            this.updateActiveTab(ProfileTabs.FAVORITE_PRODUCTS);
          }
        }),
        takeUntil(this.$destroy)
      )
      .subscribe();
  }

  createPromocode(): void {
      this.service.createPromocode().pipe(
      takeUntil(this.$destroy),
      tap(() => {
        this.getPromocodes()
      }
      ))
        .subscribe();
  }

  getPromocodes(): void {
    if(this.promocodes.length === 0) {
      this.loader = true
    }
    const pub_id = this.storageService.getData('Authorization')?.pub_id
    if (pub_id && pub_id !== 0) {
    this.service.getPromocodes().pipe(
      takeUntil(this.$destroy),
      tap(res => {
        if(res.success) {
          this.promocodes = res.data.map(p=>({...p, promocode:`${window.location.origin}/p?c=${p.promocode}`}))
          this.promocodes.reverse();
          this.loader = false;
        }
      })
    ).subscribe() }
    else {
      this.loader = false;
    }
  }

  bonusImgSrc(isActive: boolean) {
    return isActive
      ? 'assets/icons/gift_white.webp'
      : 'assets/icons/gift_green.webp';
  }

  referalImgSrc(isActive: boolean) {
    return isActive
      ? 'assets/icons/referal_white.webp'
      : 'assets/icons/referal_green.webp';
  }

  orderHistoryImgSrc(isActive: boolean) {
    return isActive
      ? 'assets/icons/order-history_white.webp'
      : 'assets/icons/order-history.webp';
  }

  checkDeviceType(): void {
    this.isMobileDevice = window.innerWidth <= 768;
  }

  handleDuplicatePhones(){
    this.phoneVerificationStatus = 1;
    this.modalService.dismissAll();
    this.router.navigate(['/home']).then(()=>this.profileData.signOut());
  }

  keyPressNumbers(event: { keyCode: number; preventDefault: () => void; }) {
    const inp = String.fromCharCode(event.keyCode);
    const phoneControl = this.phoneForm.get('phone')

    if (phoneControl && /[0-9]/.test(inp)) {
      const currentValue = phoneControl.value || '';
      if (currentValue === '' && !currentValue.startsWith(this.countryCityService.selectedCountry.phone_code)) {
        phoneControl.setValue(this.countryCityService.selectedCountry.phone_code + currentValue);
      }
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }

  public verifyRecaptcha(): Observable<string | null> {
    return this.recaptchaV3Service.execute('importantAction')
      .pipe(
        catchError(() => of(null)),
      )
  }

    public selectLocation(location: DeliveryLocation, isModal = false, content?: TemplateRef<any>): void {
      this.patchAddressWithSelectedLocation(location);
      this.updateDeliveryLocationWithZone(location.id);

    if (isModal && content) {
      this.openDeliveryConfirmationModal(content);
    }
  }

    public openDeliveryConfirmationModal(content: TemplateRef<any>): void {
    this.openMapModalRef = this.modalService.open(content, {
      size: 'xl',
      centered: true,
      keyboard: false,
    })
  }
    private patchAddressWithSelectedLocation(location: DeliveryLocation | DeliveryLocationZoneCoverage): void {
      let patchedLocation = {};
      if ('id' in location) {
        patchedLocation = { address_id: location.id, address_name: location.address, city: location.city }
      }
      if ('place_id' in location) {
        patchedLocation = { address_id: location.place_id, address_name: location.address, city: location.city }
      }
      this.form?.patchValue({ address: patchedLocation }, { emitEvent: false });
    }

  public confirmDeliveryAddress(isDesk = false): void {

    this.openMapModalRef?.close();

      //Closing the confirmation modal for mobile devices
      // if (!isDesk) {
      //   this.modalService.dismissAll();
    // }

      const updateVerificationStatue = true;
      this.updateDeliveryLocationWithZone(this.addressId?.value, updateVerificationStatue);
  }

  /* ISSUE HERE, NEEDS RESOLVING */
    private updateDeliveryLocationWithZone(place_id: string | null, updateVerificationStatus = false): void {
    if (!place_id) {
      return;
    }
    this.locationApiService.checkDeliveryCoverageByPlaceId(place_id)
      .pipe(
        take(1),
      )
      .subscribe(
        val => {
          if (!val.data?.address) {
            this.handleFalsyAddress();
            return;
          }
          this.patchAddressWithConfirmedLocation(val.data, updateVerificationStatus);
        }
      )
  }

  private patchAddressWithConfirmedLocation(location?: DeliveryLocationZoneCoverage, updateVerifiedStatus = false) {
    if (!location) {
      return;
    }
    const patchedLocation = { address_id: location.place_id, address_name: location.address, coordinates: location.coordinates, city: location.city, is_verified: updateVerifiedStatus, is_available: location.available }
    this.form.patchValue({ address: patchedLocation }, { emitEvent: false });
    this.form.updateValueAndValidity();
  }

  private handleFalsyAddress() {
    this.addressRefErrorText.set("ZONE_COVERAGE.ENTER_VALID_ADDRESS");
    this.showLocationErrorMessage();
  }

  private showLocationErrorMessage(): void {
    this.toastService.show({
      header: '',
      body: '',
      template: this.addressIsRouteErrorRef,
      classname: 'bg-danger text-light',
      autohide: false
    });
  }

    public handleMarkerMove(event: google.maps.LatLngLiteral): void {
      this.locationApiService.checkDeliveryCoverageByCoordinates(event).pipe(
        take(1),
      ).subscribe(res => {
        if (res.data) {
          this.patchAddressWithSelectedLocation(res.data);
        }
      })
  }

    private initGoogleMapId(): void {
    this.mapId = this.storageService.getData('cityInfo').google_maps_map_id
  }
    private observeAddressCtrl(): void {
      if (this.addressName) {
        this.addressName.valueChanges.pipe(
          tap(val => {
            if (!val?.trim()?.length) {
              this.locationDropdownOptions.set(null);
            }
            if (this.addressName?.dirty) {
              this.addressId?.reset();
              this.addressCoordinates?.reset();
              this.addressIsVerified?.reset();
            }
          }),
          filter(val => val?.length > 2),
          debounceTime(1500),
          switchMap(val => this.locationApiService.getLocationAutocompleteOptions(val)),
          tap((response) => {
            if (response.success)
              this.locationDropdownOptions.set(response.data?.predictions || []);
          }),
        ).subscribe();
      }
    }

  stopModalClickPropagation(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
    event.stopImmediatePropagation();
  }

  public handleSelectedWheelSegment(segment: WheelSegment | null) {
    this.wheelService.requestWheelSpinResult()
  }

  public wheelClickHandler(action:WheelAction) {
    this.wheelService.handleWheelAction(action);
  }

  private initAppleSignIn(): void {
    AppleID.auth.init({
      clientId: appleConfig.clientId,
      scope: 'name email',
      redirectURI: appleConfig.redirectURI,
      usePopup: true,
      responseType: 'code id_token',
      responseMode: 'fragment'
    });
  }

  private decodeJWT(token: string): { sub: string;  email:string} {
    const payload = token.split('.')[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  }

    public onAppleLoginClick(): void {
      this.verifyRecaptcha().pipe(
        switchMap(recaptchaToken => {
            // Convert the Apple sign-in promise to an observable
            return from(AppleID.auth.signIn()).pipe(
              switchMap((response: any) => {
                if (response.error) {
                  return of(EMPTY);
                }

                const idToken = response?.authorization?.id_token;
                const userData = response?.user;// Only available the first time
                const userName = userData ? `${userData?.name?.firstName} ${userData?.name?.lastName}`:'';
                
                const payload = {
                  city_id: this.storageService.getData('city'),
                  name: userName,
                  email: userData?.email || this.decodeJWT(idToken).email,
                  recaptchaToken
                };

                return this.profileData.appleSignIn(payload);
              })
            );
        }),
        take(1),
        takeUntil(this.$destroy)
      ).subscribe({
        next: () => {
          if (
            this.profileData.Authorization &&
            !this.profileData.Authorization?.phone
          ) {
            this.phoneVerificationStatus = 1;
              this.openModal(this.selectPhone, 'sm');
            }
            this.loader = false;
          }
        });
    }
}
