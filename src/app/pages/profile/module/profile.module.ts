import { NgModule } from '@angular/core';
import { CommonModule, NgTemplateOutlet } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ProfileComponent } from '../components/profile.component';
import { ProfileRestService } from '../services/profile-rest.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Shell } from '../../../shell/shell.service';
import { DimensionsModule } from '../../../shared/directives/dimensions.module';
import { ProductItemModule } from 'src/app/shared/product-item/product-item.module';
import { ShareButtonsModule } from 'ngx-sharebuttons/buttons';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { ProductCardModule } from 'src/app/shared/product-card/product-card.module';
import { ShareButtonsComponent } from '../components/share-buttons/share-buttons.component';
import { NgbToastModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastService } from 'src/app/shared/utils/services/toast.service';
import { ToastsContainer } from 'src/app/toastes';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { MapComponent } from "../../../shared/map/map.component";
import { LocationsDropdownOptionComponent } from "../../../shared/locations-dropdown-option/locations-dropdown-option.component";
import { WheelOfFortuneComponent } from 'src/app/shared/wheel-of-fortune/wheel-of-fortune.component';


const routes: Routes = Shell.childRoutes([
  {
    path: '',
    component: ProfileComponent,
  },
]);

@NgModule({
  declarations: [
    ProfileComponent,
    ShareButtonsComponent
  ],
  providers: [ProfileRestService, ToastService, provideNgxMask()],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    TranslateModule,
    FormsModule,
    NgxMaskDirective,
    NgxMaskPipe,
    DimensionsModule,
    ProductItemModule,
    ShareButtonsModule,
    ClipboardModule,
    ProductCardModule,
    NgbToastModule,
    NgTemplateOutlet,
    ToastsContainer,
    MapComponent,
    LocationsDropdownOptionComponent,
    WheelOfFortuneComponent
],
})
export class ProfileModule {}
