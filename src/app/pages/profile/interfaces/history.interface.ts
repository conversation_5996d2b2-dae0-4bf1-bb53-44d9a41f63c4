import {ProductData} from "../../home/<USER>/home.interface";

export interface HistoryInterface {
  success: boolean,
  data: HistoryData[]
}
export interface HistoryData {
  futuredate: string,
  futuretime: string,
  id: number,
  total?: number,
  address?: string
  delivery_type?: string,
  payment_type?: PaymentType,
  products?: ProductData[]
}

export enum PaymentType {
  cash = 'cash',
  card = 'card'
}
