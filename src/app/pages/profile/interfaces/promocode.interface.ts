import { ProductData } from "../../home/<USER>/home.interface";

export interface Promocode {
    id: number;
    referer_id: number;
    promocode: string;
    amount: number;
    onetime: number;
    active: number;
    usages: number;
    created_at: string;
    updated_at: string;
    status:PromoStatus
}

export interface PromoResponse {
    success: boolean;
    data: Promocode[];
  }

export interface PromoPreviewResponse {
    success:boolean;
    data?:{
        onetime:number;
        product:ProductData
    };
    message?:string
}

export enum PromoStatus {
    PENDING = 'pending',
    WAITING = 'waiting',
    COMPLETED = 'completed'
}