.align {
  display: flex;
  justify-content: center;;
  height: 100vh;
  position: relative;
}

.checkout-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  width: 20rem;
  height: 24rem;
}

.text {
  margin: 2rem 0;
  color: var(--text-primary);
  font-weight: bold;
}

.logo {
  margin: 1rem;
}
