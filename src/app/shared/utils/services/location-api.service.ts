import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { ApiResponse } from '../../interfaces/api-response.interface';
import { AutocompleteResponseWithSessionToken, DeliveryLocationZoneCoverage } from '../../interfaces/delivery-location.interface';
import { StorageLogicService } from './storage-logic.service';
import { AppApiUrls } from 'src/app/app.api.urls';

@Injectable({
  providedIn: 'root'
})
export class LocationApiService {

  private readonly http = inject(HttpClient);
  private readonly storageService = inject(StorageLogicService);

  private sessionToken: string | null = null;

  public updateSessionToken(token:string | null) {
    this.sessionToken = token;
  }

  public getLocationAutocompleteOptions(address: string):Observable<ApiResponse<AutocompleteResponseWithSessionToken>> {
    return this.http.get<ApiResponse<AutocompleteResponseWithSessionToken>>(AppApiUrls.locationsAutocomplete(), {
      params: {
        city_id: this.storageService.getData('city')!,
        lang: "en",
        address,
        v: 2,
        ...this.sessionToken && { session_token: this.sessionToken }
      }
    })
      .pipe(
        tap((response) => this.updateSessionToken(response?.data?.session_token ?? null)),
      );
  }

  public checkDeliveryCoverageByPlaceId(id: string):Observable<ApiResponse<DeliveryLocationZoneCoverage>> {
    return this.http.get<ApiResponse<DeliveryLocationZoneCoverage>>(AppApiUrls.zoneCoverageByPlaceId(), {
      params: {
        city_id: this.storageService.getData('city')!,
        lang: "en",
        place_id: id,
        ...this.sessionToken && { session_token: this.sessionToken }
      }
    })
      .pipe(
        tap(() => this.updateSessionToken(null))
      );
  }

  public checkDeliveryCoverageByCoordinates(coordinates:google.maps.LatLngLiteral):Observable<ApiResponse<DeliveryLocationZoneCoverage>> {
      return this.http.get<ApiResponse<DeliveryLocationZoneCoverage>>(AppApiUrls.zoneCoverageByCoordinates(), {
        params: {
          city_id: this.storageService.getData('city')!,
          lang: "en",
          lat: coordinates.lat,
          lng:coordinates.lng
        }
    });
  }

  public getUnverifiedAddressCoordinates(id:number):Observable<ApiResponse<DeliveryLocationZoneCoverage>>  {
      return this.http.get<ApiResponse<DeliveryLocationZoneCoverage>>(AppApiUrls.unverifiedAddressCoordinates(), {
        params: {
          city_id: this.storageService.getData('city')!,
          lang: "en",
          address_id:id
      }
    });
  }
}
