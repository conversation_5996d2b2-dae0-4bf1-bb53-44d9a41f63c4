.filter-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.filter-title {
  color: var(--text-primary);
  font-size: 12px;
  border: none;
  margin-right: 5px;
}

.filter-mobile {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-input {
  width: 100%;
  .dropdown-block{
    width: 100%;
  }
  .btn-clear-input {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    @media (prefers-color-scheme:dark) {
      color: var(--dark-theme-button-color);
    }
  }
}


.filter-reset{
  float: right;
  margin-right: 1rem;
  margin-bottom: .5rem;

  @media (prefers-color-scheme:dark) {
    color: var(--button-color);
  }
}

.hidden {
  visibility: hidden;
}

.search {
  color: var(--primary);
  width: 100%;
  border: none;
  border-radius: 2rem;
  padding: 0.2em 1em 0.4em 1em;
  background-color: var(--select);
  transition: all .3s ease;

  &:hover {
    background-color: var(--input-background-hover);
  }
}

::placeholder {
  font-size: .7em;
  text-align: center;
  @media (prefers-color-scheme:dark) {
    color: var(--dark-theme-sprimary-color);
  }
}

.drop-menu {
  width: 3.8rem;
  border: 2px solid var(--marker-primary);
  border-radius: 10rem;
  box-shadow: none;
  min-width: 0;
  z-index: 0;
}


.drop-menu-filter {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  margin: 1rem 0;
  border: none;
  left: 1rem;
  top: 3rem;
  width: 100%;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: black;
  padding: 4px 0;
  box-shadow: none;
  transition: all .2s ease;
}

.drop-menu-button:hover {
  width: calc(100% - .4rem);
  background: var(--button-primary-hover);
  color: var(--button-color);
  border-radius: 10rem;
  margin: 1rem .2rem;
  transition: all .5s ease;
}

.marking-filter {
  width: 1.8rem;
  height: 1.8rem;
}

@media screen and (max-width: 768px) {

  .filter-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-mobile {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
  }
}

@media screen and (max-width: 480px) {
  .filter-container {
    margin-top: 1rem;
  }

  .text-width-mobile{
    max-width: 70px;
  }
}

@media screen and (max-width: 1200px) {
  .filter-title {
    display: block;
    margin-bottom: .5rem;
  }
}

@media (min-width: 1200px) and (max-width: 1400px) {
  .filter-mobile {
    padding: 0;
  }

  .ms-2.cursor-pointer {
    margin-left: 0 !important;
  }
}



