.marking {
  width: 2rem;
  height: 2rem;
  margin: 1rem 0 0.5rem 1rem;
}

.product-card {
  justify-content: center;
  &-img {
    max-width: 20rem;
    max-height: 25rem;
    object-fit: contain;
  }
}

.sale-price {
  font-size: 13px;
  margin: 0;
  &__sum {
    text-decoration: line-through;
    font-weight: bold;
    color: var(--text-primary)
  }
  &__percent {
    margin-left: 5px;
    border-radius: 30rem;
    font-weight: bold;
    padding: 0.1rem 0.5rem;
    background-color:  var(--text-danger);
    color: var(--main) !important;
  }
}

.allergen {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  &-view {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
  }
  &-view-mobile {
    display: none;
  }
  &-img {
    max-height: 1.7rem;
    max-width: 1.7rem;
  }
}

span {
  color: var(--text-primary);
}

.price {
  span {
    font-size: 23px;
    color: var(--text-accent);

    @media (prefers-color-scheme: dark) {
      color: var(--text-primary);
    }

    &.currency {
      font-size: 1.1rem;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}

.margin-top-13 {
  margin-top: 13rem !important;
}

.instagram-icon {
  width: 2.5rem;
  height: 2.5rem;
}

.instagram {
  width: 5rem;
  height: 5rem;
  border-radius: 1rem;
  background: url("https://storage.3303.ua/assets/instagram/1627051511.jpg");
  object-fit: cover;
}

.pieces-container {
  background: var(--marker-primary);
  color: var(--marker-color);
  padding: .4rem .8rem;
  border-radius: 3rem;
}
.pieces {
  color: inherit;
}

.weight-container {
  flex: 0 0 auto;
  width: 10.66666667%;
  display: table-cell;
  vertical-align: middle;
  color: var(--marker-secondary-color);
  background: var(--marker-secondary);
  border: none;
}

.drop-menu {
  width: 3.8rem;
  border: 2px solid var(--marker-secondary-border);
  border-radius: 10rem;
  box-shadow: none;
  min-width: 0;
  z-index: 0;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: var(--marker-secondary-border);
  padding: 0;
  box-shadow: none;
}

.drop-menu-button:hover {
  background: var(--marker-primary-hover);
  color: var(--marker-color);
}

.ing {
  width: 8%;
  height: auto;
  margin-left: .3rem;
  margin-right: .3rem;
}

.header {
  font-weight: bold;
  margin-bottom: 1.5rem;
  margin-top: 1rem;
}

.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;

  &__text {
    margin-left: 5px;
    color: inherit;
  }

  &:hover {
    background: var(--button-primary-hover);
  }
}
.text-center {
  text-align: left !important;
}
.justify-content-center {
  justify-content: left !important;
}

.loader {
  color: var(--loader-color);
  font-size: 10px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  -webkit-animation: load4 1.3s infinite linear;
  animation: load4 1.3s infinite linear;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
.loader-margin-img {
  margin: 10rem auto;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  &__favorite {
    border: none;
    background-color: transparent;
    padding: 0;

    svg {
      height: 100%;
    }
  }
}

.instagram-mobile {
  margin: 1rem 0;
  display: flex;
  align-items: center;
}

.nutrition {
  width: 50%;
}

.countdown {
  margin-bottom: 24px;
}

@media screen and (max-width: 991px) {

  .nutrition {
    width: 75%;
    &:nth-child(2) {
      margin-top: 1rem;
    }
  }

  .mobile-align {
    margin-top: 1rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .allergen {
    margin-left: 2rem;
    margin-right: 2rem;
    align-items: center;
    justify-content: space-between;
    &-view {
      display: none;
    }
    &-view-mobile {
      display: block;
      .row {
        justify-content: center !important;
      }
    }
  }

  .weight {
    width: 100%;
  }
  .product-card-mobile {
    display: flex;
    flex-direction: column;
  }
  .instagram-mobile {
    justify-content: center;
  }
  .instagram {
    width: 4.5rem;
    height: 4.5rem;
  }
  .align-mobile {
    justify-content: space-evenly;
  }
  .ing {
    width: 15% !important;
    height: auto;
  }
  .text-center {
    text-align: center !important;
    justify-content: center;
  }
  .justify-content-center {
    justify-content: center !important;
  }
  .col-8 {
    flex: 0 0 auto;
    width: 100%;
  }
  .col-4 {
    flex: 0 0 auto;
    width: 100%;
  }
  .col-1 {
    flex: 0 0 auto;
    width: 40%;
  }
  .row {
    margin: 0 !important;
    flex-direction: column;
    align-items: center;
  }
}

.sb-moon-theme {
  // share button wrapper
  .sb-wrapper {
    // Content wrapper
    .sb-content {
      width: 10px;
      background: var(--primary);
      .sb-icon {
      }
      // Text wrapper
      .sb-text {
      }
    }

    // For conditional styles
    // E.g. Apply only when icon, text are shown
    &.sb-show-icon.sb-show-text {
      // Icon wrapper
      .sb-icon {
      }
      // Text wrapper
      .sb-text {
      }
    }
  }
}
