import { Component, Input, input, output, signal } from '@angular/core';
import { GoogleMapsModule } from '@angular/google-maps';


@Component({
  selector: 'app-map',
  standalone: true,
  imports: [GoogleMapsModule],
  templateUrl: './map.component.html',
  styleUrl: './map.component.scss',
})
export class MapComponent {
  @Input() set coordinates(location: google.maps.LatLngLiteral | null) {
    if (!location) {
      return;
    }
    this.placeMarker(location);
  }

  mapId = input<string | null>(null);

  public markerPosition = signal<google.maps.LatLngLiteral | null>(null);
  
  public mapCenter = signal<google.maps.LatLngLiteral | null>(null);

  protected readonly markerOptions: google.maps.marker.AdvancedMarkerElementOptions = { gmpDraggable: true };

  public options = input<google.maps.MapOptions>({})

  public moveMarker = output<google.maps.LatLngLiteral>();

  private placeMarker(coordinates: google.maps.LatLngLiteral) {
    this.markerPosition.set(coordinates);
    this.mapCenter.set(coordinates);
  }

  public changeLocation(event: google.maps.MapMouseEvent):void {
    const coordinateFns = event.latLng
    if (coordinateFns) {
      this.moveMarker.emit({ lat: coordinateFns.lat(), lng: coordinateFns.lng() });
      this.mapCenter.set({ lat: coordinateFns.lat(), lng: coordinateFns.lng() })
    }
  }
}
