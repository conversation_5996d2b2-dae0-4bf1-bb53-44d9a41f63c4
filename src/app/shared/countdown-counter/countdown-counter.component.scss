.countdown-wrapper {
    position: relative;
    z-index: 0;
    max-width: 300px;
    width: 100%;
}

.countdown-padding {
    padding-inline: 24px;
}

.backdrop {
    background-color: grey;
    border-radius: 5px;
    opacity: 0.1;
    position: absolute;
    z-index: -1;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.countdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeblock {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1;
    font-size: 12px;

    &__accent {
        color:#FF4040;
        font-weight: 700;
        font-size: 16px;
    }
}

:host {
    width: 100%;
    display: flex;
    justify-content: center;

    p {
        margin:0;
    }

    .gallery-view {
        background-color: #f3f5f4;
        border: 1px solid #e8eae9;
        border-radius: 100vw;
        padding-block: 4px;
    }

    .card-view {
        border-radius: 5px;
        padding-block: 8px;
    }

    .order-view {
        
        .countdown__icon {
            width: 16px;
        }

        .timeblock{
            font-size: 12px;

            &__accent {
                font-size: 14px;
            }
        }
    }

    .gallery-sm-view {
        @extend .gallery-view;
        
        padding-inline: 12px;
        .timeblock {
            font-size: 14px;
                
            &__accent {
                font-size: 12px;
            }
        }
    }
}

@media (max-width: 768px){
    .gallery-sm-view {
        .timeblock {
            font-size: 10px;
    
            &__accent {
                font-size: 12px;
            }
        }
    }
}

@media (max-width: 991px) {
    .gallery-view {
        padding-inline: 12px;
    
        .timeblock {
            font-size: 10px;
    
            &__accent {
                font-size: 12px;
            }
        }
    }
}

