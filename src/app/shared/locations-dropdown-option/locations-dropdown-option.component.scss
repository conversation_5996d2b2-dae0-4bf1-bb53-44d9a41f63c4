:host {
    .location {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 14px;
        font-size: 13px;

        &__icon {
            width: 19px;
            height: 22px;
        }

        .address{
            text-align: left;
            padding-right: 24px;

            p{
                margin-bottom: 0;
            }

            &__route{
                color: var(--primary);
                margin-bottom: 4px;
                text-wrap: nowrap;
                text-overflow: ellipsis;
            }
            
            &__city {
                color: var(--text-primary);
            }

            &__not-found{
                color:red;
            }
        }
    }

@media (prefers-color-scheme:dark) {
    .location{
        &__icon{
            fill:inherit;
        }
    }
        .address {
                &__city {
                    color: var(--dark-theme-sprimary-color) !important;
                }
            }
}
}


