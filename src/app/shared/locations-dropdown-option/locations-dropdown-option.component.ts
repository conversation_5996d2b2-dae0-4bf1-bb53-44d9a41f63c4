import { SlicePipe } from '@angular/common';
import { Component, input } from '@angular/core';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { DeliveryLocationInputData } from 'src/app/shared/interfaces/delivery-location.interface';

@Component({
  selector: 'app-locations-dropdown-option',
  standalone: true,
  imports: [SlicePipe, NgbTooltipModule],
  templateUrl: './locations-dropdown-option.component.html',
  styleUrl: './locations-dropdown-option.component.scss'
})
export class LocationsDropdownOptionComponent {
  public location = input.required<DeliveryLocationInputData | undefined>();
  
  public showTooltip = input<boolean>(false);
}
