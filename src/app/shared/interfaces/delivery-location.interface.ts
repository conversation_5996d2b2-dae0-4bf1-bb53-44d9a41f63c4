export interface DeliveryLocation {
  id: string;
  full_address: string;
  address: string;
  city: string;
  is_route:boolean
}

export interface AutocompleteResponseWithSessionToken {
  session_token: string;
  predictions: DeliveryLocation[]
}

export type DeliveryLocationInputData = Pick<DeliveryLocation, 'address' | 'city'>;

export interface DeliveryLocationZoneCoverage {
  available: boolean;
  place_id: string;
  address: string;
  full_address: string;
  coordinates: google.maps.LatLngLiteral;
  zone: DeliveryZone | null;
  city: string;
  is_route: boolean;
  is_address_incomplete: boolean;
}

export interface DeliveryZone{
  id: number;
  max_time: string;
  min_price: number;
  min_time: string;
  name: string;
}