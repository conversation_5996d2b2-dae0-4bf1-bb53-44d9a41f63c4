.product-card {
  background: var(--block-background);
  box-shadow: var(--block-box-shadow);
  border-radius: 20px;
  border: none;
  transition: all .3s ease;

  &:hover {
    transform: scale(1.03, 1.03);
  }
}

.white-container {
  border-radius: 20px 20px 0 0;
  background: white;
  height: 13rem;
  display: flex;
  align-items: center;
  position: relative;
}

.marking-wrapper{
  position: absolute;
  width: 100%;
  top: 0;
}

.marking {
  width: 2rem;
  height: 2rem;
  margin: 1rem 0 0.5rem 1rem;
  color: var(--primary-color)
}

.instagram {
  width: 2rem;
  height: 2rem;
  margin: 1rem 1rem 0.5rem 1rem;
}

.sale-price {
  margin: 0;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;

  &__sum {
    text-decoration: line-through;
    font-weight: bold;
    color: var(--text-primary)
  }

  &__percent {
    margin-left: 5px;
    border-radius: 30rem;
    font-weight: bold;
    padding: 0.1rem 0.5rem;
    background-color: var(--text-danger);
    color: var(--main) !important;
    text-wrap-mode: nowrap;
  }
}

.dropdown-toggle::after {
  color: var(--marker-primary);
  position: absolute;
  right: -10px;
}

.weight-wrapper{
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  top: 30%;
  right: 6%;
  position: absolute;
}

.weight {
  background: var(--marker-primary);
  color: var(--marker-color);
  width: 3.8rem;
  height: 3.8rem;
  border-radius: 10rem;
  border: none;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all .3s ease;
  z-index: 1;
  position: relative;

  &:hover {
    background: var(--marker-primary-hover);
  }
}


.checkmark {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  height: 3.8rem;
  width: 3.8rem;
  background-color: var(--marker-secondary);
  border-radius: 50%;
  border: 2px solid var(--marker-secondary-border);
  color: var(--marker-secondary-color);
  transition: all .2s ease-in-out;

  &:hover {
    color: var(--marker-color);
    border: 2px solid var(--marker-primary-hover);
    background-color: var(--marker-primary-hover)
  }
}

input:checked~.checkmark {
  background-color: var(--marker-primary);
  color: var(--marker-color);
}

.checkmark:after {
  position: absolute;
  display: none;
}

input:checked~.checkmark:after {
  display: block;
}

.weight-second {
  width: 3.9rem;
  font-size: 14px;
  display: table;
  transition: all .3s ease;
  position: relative;
}

.weight-marker {
  position: absolute;
  top: 14%;
  .dropdown-menu {
    width: 3.8rem;
  }
}

.countdown {
  position: absolute;
  bottom: 8px;
  width: 80%;
  left:50%;
  transform: translateX(-50%);
}

.countdown__gallery-sm{
  width: 100%;
  max-width: 156px;
}

.drop-menu {
  padding-top: 55px;
  position: absolute !important;
  left: auto !important;
  top: -3px !important;
  right: -3px !important;
  transform: none !important;
  will-change: unset;
  width: 4.2rem !important;
  border-radius: 10rem;
  border: none;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  min-width: 0;
  z-index: 0;
}

.drop-menu-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  color: var(--input-outline);
  padding: 0;
  box-shadow: none;
}

.drop-menu-button:hover {
  background: var(--marker-primary-hover);
  color: var(--marker-color);
}

.name {
  height: 3rem;
}

.product-name {
  font-weight: bold;
  font-size: 1rem;
  color: var(--text-primary);
}

.ing {
  height: 45px;
  width: 17%;
  margin-left: .3rem;
  margin-right: .3rem;
}

.price {
  font-weight: bold;
  font-size: 1.5rem;
  color: var(--text-accent);
  .currency{
    font-size: 1.1rem;
  }
}

.product-img {
  cursor: pointer;
  width: 15rem;
  height: 10rem;
  object-fit: contain;
  transition: all 1s ease;
  max-width: 100%;
}

.green-button {
  color: var(--button-color);
  background: var(--button-primary);
  border-radius: 1rem;
  padding-top: .6rem;
  padding-bottom: .6rem;
  transition: all .4s ease;

  img {
    float: left;
    margin-right: 5px;
  }

  &:hover {
    background: var(--button-primary-hover);
  }
}

.favorite-button {
  border: none;
  background-color: transparent;
  position: absolute;
  right: 1rem;
  top: 1rem;
  padding: 0;
}

:host ::ng-deep {
  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    filter: invert(100%);
    width: 20px;
    height: 20px;
    background-size: contain;
    @media (prefers-color-scheme:dark) {
      // DON'T DELETE! RESTORE TO INVERT(0) FOR LIGHT APPEARANCE
      filter: invert(100%);
    }
  }

  ngb-carousel .carousel-indicators {
    display: none !important;
  }
}
.carousel-height {
  min-height: 3.8125rem;
}

@media screen and (max-width: 768px) {
  .white-container {
    height: 12rem;
  }

  .ingredients {
    display: none;
  }

  .checkmark {
    width: 3.7rem;
    height: 3.7rem;
  }

  .weight {
    width: 3.7rem;
    height: 3.7rem;
  }

  .weight-second {
    width: 3.7rem;
    height: 3.7rem;
    top: 32%;
  }

  .drop-menu {
    width: 4.1rem !important;
  }

  .product-img {
    width: 8rem;
    height: 8rem;
  }

  .product-button {
    font-size: .9rem;
  }

  .product-name{
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2.4em;
    line-height: 1.2em;
    max-width: 100%;
  }

}

@media screen and (max-width: 480px) {
  :host ::ng-deep carousel-inner{
    height: 29px;
  }

  .ing {
    width: 14%;
  }

  .weight {
    width: 2.7rem;
    height: 2.7rem;
    font-size: 10px;
  }

  .carousel-height{
    display: none;
  }


  .price {
    font-weight: normal;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    .currency{
      font-size: 1rem;
    }
  }
}

@media screen and (max-width: 1200px) {

  .product-img {
    width: 10rem;
  }
}

.favorite-product {
  &__img {
    width: 100% !important;
  }
}
