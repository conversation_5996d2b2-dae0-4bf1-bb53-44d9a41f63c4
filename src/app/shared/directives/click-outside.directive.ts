import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Output,
} from '@angular/core';

@Directive({
  selector: '[appClickOutside]',
})
export class ClickOutsideDirective {
  @Output() clickOutside = new EventEmitter<HTMLElement>();
  @HostListener('document:click', ['$event.target'])
  public onClick(target: HTMLElement) {
    if (!this.ref.nativeElement.contains(target)) {
      this.clickOutside.emit(target);
    }
  }
  constructor(private readonly ref: ElementRef<HTMLElement>) {}
}
