import { Component, inject } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-preorder-modal',
  standalone: true,
  imports: [TranslatePipe],
  templateUrl: './preorder-modal.component.html',
  styleUrl: './preorder-modal.component.scss',
})
export class PreorderModalComponent {
  activeModal = inject(NgbActiveModal);
}
