:host {
    .modal-close {
        border: none;
        background-color: transparent;
        margin-left: auto;
    }

    .modal-header{
        border-bottom: none;
    }
    
    .modal-body {
        padding: 0 36px 62px;

        @media (min-width: 769px) {
            padding: 0 87px 70px;
        }

        .subtitle{
            color: var(--text-primary);
            font-weight: 500;
           
            ::ng-deep span.text-accent{
                color: var(--text-accent);
                font-weight: bold;
            }

            @media (min-width:769px) {
               position: absolute;
               z-index: 1;
               max-width: 44%; 
            }
        }
    }

    .modal-title{
        text-align: center;
        color: var(--text-title);
        font-weight: bold;
        margin-bottom: 24px;
    }

    .image-container{
        @media (min-width:769px) {
            margin-top: 80px;
        }

        .preorder-img {
            display: block;
            margin-inline: auto;
            width: 200px;
            position: relative;
            z-index: 1;
            left: 8%;

            @media  (min-width:769px) {
                width: 306px;
            }  
        }

        button {
            display: block;
            margin-inline: auto;
            margin-top: -6px;
        }

        .green-button {
            color: white;
            background: var(--primary);
            border-radius: 1rem;
            padding-top: .6rem;
            padding-bottom: .6rem;
            transition: all .4s ease;
            text-wrap-mode: nowrap;
          
            &:hover {
              background: #12A95D;
            }
          }
        
          .product-button {
            font-size: 18px;
          }
    }
}