import { WheelConfig, WheelAction, WheelSegment, WheelSpinResponse, WheelBonus } from "../models/wheel-of-fortune.model";


export const wheelOfFortuneConfig = (userAuthorized = false, spinsLeft: number, bonusesLeft: number, won=false) => {
    const config:WheelConfig = {
        header: wheelHeaders.default(),
        description: wheelDescriptions.default(),
        buttons:[{
            action: WheelAction.AUTHORIZE
        }]
    };
    if (!userAuthorized) {
        return config;
    }

        config.subtitle = wheelSubtitles.default(spinsLeft);
        if (!spinsLeft) {
            config.header = wheelHeaders.outOfSpins(bonusesLeft);
            config.buttons = [
                {
                    action: bonusesLeft ? WheelAction.BUY_SPINS : WheelAction.ORDER,
                    text: bonusesLeft ? 'Купити оберти' : 'Зробити замовлення',
                    type: bonusesLeft ? 'secondary' : 'primary'
                }
            ]
        } else {
            config.buttons = [
                {
                    action: WheelAction.SPIN,
                    text: 'Тягніть вправо для оберту',
                    type:'primary'
                }
            ]
    }
    
    if (won) {
        config.header = wheelHeaders.default();
        if (spinsLeft) {
            config.buttons = [{ action: WheelAction.SPIN, text: 'Тягніть вправо для оберту', type: 'primary' }]
            config.subtitle = wheelSubtitles.default(spinsLeft);
        }
        else if (bonusesLeft) {
            config.buttons = [
                {
                    action:  WheelAction.BUY_SPINS,
                    text:'Купити оберти',
                    type: 'secondary'
                }
            ]
        }
        else {
            config.buttons = [
                { action: WheelAction.NAVIGATE_TO_HOME, text: 'Оформити замовлення', icon:'assets/icons/cart-white.webp', type: 'primary' },
                { action: WheelAction.NAVIGATE_TO_WHEEL, text: 'До колеса', icon:'assets/icons/return.svg', type: 'secondary' }
            ]
            config.subtitle = undefined;
        }
    }

    return config;
}

export const wheelTextClassMaker = (type:'header'|'description'|'subtitles') => {
    switch (type) {
        case 'header':
            return ['clr-accent', 'fsz-lg', 'fw-bold'];
        case 'description':
            return ['clr-primary', 'fsz-md'];
        case 'subtitles':
            return ['clr-primary', 'fsz-md'];
        default:
            return [];
    }
}

export const wheelHeaders = {
    default: () => ({ text: 'WHEEL.HEADER.DEFAULT', translateKeyValuePairs: {}, classes: wheelTextClassMaker('header') }),
    spinMarket: (bonuses:number) => ({text: 'WHEEL.HEADER.SPIN_MARKET', translateKeyValuePairs: { bonuses }, classes: wheelTextClassMaker('header') }),
    afterOrder: () => ({ text: 'WHEEL.HEADER.AFTER_ORDER', translateKeyValuePairs: {}, classes: wheelTextClassMaker('header') }),
    outOfSpins: (bonuses: number) =>  ({text: bonuses?'Ваші оберти Колеса Фортуни скінчилися, придбайте оберти за бонуси!':'Ваші оберти Колеса Фортуни скінчилися,зробіть замовлення!', translateKeyValuePairs: {},classes: wheelTextClassMaker('header') })
}

export const wheelDescriptions = { 
    default: (minOrder = 300) => ({ html: 'WHEEL.DESCRIPTION.DEFAULT', translateKeyValuePairs: {
        minOrder
    }, classes:wheelTextClassMaker('description') }),

}

export const wheelSubtitles = {
    default: (spins: number) => ({
        text: `У вас лишилося: ${spins} обертів`,
        translateKeyValuePairs: {
            spins
        },
        classes: wheelTextClassMaker('subtitles')
    })
}

export const wheelSegmentInput:WheelSegment[] = [
    {
    text: '5',
    imgSrc: 'assets/coins.svg',
    type: 'bonuses',
    color:'grey'
    },
    {
    imgSrc: 'assets/menu/roll.webp',
    type: 'free_product',
    color: '#C74C4D',
    },
    {
    text: '0',
    imgSrc: 'assets/coins.svg',
    type: 'bonuses',
    color:'grey'
    },
    {
    imgSrc: 'assets/menu/roll.webp',
    type: 'free_product',
    color: '#C74C4D',
  },{
    text: '10',
    imgSrc: 'assets/coins.svg',
    type: 'bonuses',
    color:'grey'
    },
    {
    imgSrc: 'assets/menu/wok.webp',
    type: 'free_product',
    color:'#C74C4D'
    },
    {
    text: '50',
    imgSrc: 'assets/coins.svg',
    type: 'bonuses',
    color:'grey'
    },
    {
    imgSrc: 'assets/menu/set.webp',
    type: 'free_product',
    color:'#C74C4D'
    }
]

    export const mockResponseWithSpins:WheelSpinResponse = {
      spins: 1,
      bonuses: 0,
      trophy: {
        type: 'bonuses',
        text:'Нагіро Урамакі',
        daysTillExpiration: 12,
        imgSrc: 'assets/cashback-guy.png',
        color:''
      }
    }

    export const mockResponseWithBonuses:WheelSpinResponse = {
      spins: 1,
      bonuses: 5,
      trophy: {
        type: 'free_product',
        text:'Нагіро Урамакі',
        daysTillExpiration: 12,
        imgSrc: 'https://storage.3303.ua/assets/3303/crmimages/rolls/newroll.webp',
        color:''
      }
}
    
    export const mockResponseWithNoSpins:WheelSpinResponse = {
      spins: 0,
      bonuses: 2,
      trophy: {
        type: 'bonuses',
        text:'5',
        daysTillExpiration: 12,
        imgSrc: 'assets/cashback-guy.png',
        color:''
      }
}
    
    export const mockBonuses: WheelBonus[]= [
        {
            amount: 10,
            spinsCanBuy: 1
        },
        {
            amount: 45,
            spinsCanBuy: 5,
            formerPrice: 50,
            discountPercentage: 5
        }
    ]