import { Component, input } from '@angular/core';
import { WheelButtonConfig } from '../../models/wheel-of-fortune.model';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-wheel-button',
  standalone: true,
  imports: [NgClass],
  templateUrl: './wheel-button.component.html',
  styleUrl: './wheel-button.component.scss'
})
export class WheelButtonComponent {
  public data = input.required<WheelButtonConfig>();
  public disabled = input(false);
}
