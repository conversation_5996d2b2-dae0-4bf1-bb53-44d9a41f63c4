:host {
    display: block;
    width: 100%;
    
    .button {
        border: none;
        border-radius: 100vw;
        width: 100%;
        height: 52px;
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-inline: auto;
        transition: all 300ms ease-in;

        &--primary {
            background-color: var(--button-primary);
            color: var(--button-color);
        }

        &--secondary {
            background-color: var(--button-select);
            color: var(--text-primary);
        }

        &:disabled {
            opacity: .6;
        }
    }
}