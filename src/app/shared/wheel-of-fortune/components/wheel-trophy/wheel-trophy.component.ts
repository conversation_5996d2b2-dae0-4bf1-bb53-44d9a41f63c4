import { Component, input } from '@angular/core';
import { WheelSegment } from '../../models/wheel-of-fortune.model';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-wheel-trophy',
  standalone: true,
  imports: [TranslatePipe],
  templateUrl: './wheel-trophy.component.html',
  styleUrl: './wheel-trophy.component.scss'
})
export class WheelTrophyComponent {
  public data = input.required<WheelSegment | null>();
}
