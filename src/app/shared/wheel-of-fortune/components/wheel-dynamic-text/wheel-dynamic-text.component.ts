import { Component, DestroyRef, inject, input, OnInit, signal } from '@angular/core';
import { WheelDynamicText } from '../../models/wheel-of-fortune.model';
import { TranslateService } from '@ngx-translate/core';
import { DomSanitizer } from '@angular/platform-browser';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-wheel-dynamic-text',
  standalone: true,
  imports: [NgClass],
  templateUrl: './wheel-dynamic-text.component.html',
  styleUrl: './wheel-dynamic-text.component.scss'
})
export class WheelDynamicTextComponent implements OnInit{
  public data = input.required<WheelDynamicText>();

  public translatedText = signal('');

  private readonly domSanitizer = inject(DomSanitizer);

  private readonly translate = inject(TranslateService);

  private readonly destroyRef = inject(DestroyRef);

  ngOnInit() {
    this.translateInputText();
  }

  private translateInputText(): void {
      const { html, text, translateKeyValuePairs} = this.data();
      this.translate.get(html||text||'', translateKeyValuePairs)
        .pipe(
          tap((res) => this.translatedText.set(res)),
          takeUntilDestroyed(this.destroyRef)
        )
        .subscribe();
    }
}
