:host {
    .bonus {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        background: var(--block-background);
        box-shadow: 0px 4px 4px 3px rgba(0, 0, 0, 0.1);
        border-radius: 14px;
        padding: 10px 18px 18px;
        height: 200px;
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 134px;

        p {
            margin: 0;
        }

        &__img {
            width: 100%;
            max-width: 86px;
            display: block;
            margin-inline: auto;
        }

        &__description {
            color: var(--text-primary);
            margin-bottom: 16px;
        }


        &__price {
            color: var(--text-accent);
            margin-bottom: 8px;
        }

        &__button {
            border: none;
            background-color: var(--button-primary);
            border-radius: 8px;
            color: var(--main);
            font-size: inherit;
            display: block;
            margin-inline: auto;
            height: 26px;
            padding: 4px 16px;
        }

        .discount {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px;

            &__former-price {
                text-decoration: line-through;
                text-decoration-color: var(--text-primary);
            }

            &__percent {
                background-color: var(--button-danger);
                border-radius: 100vw;
                padding: 2px 4px;
                color: var(--main);
            }
        }

        .mt-auto {
            margin-top: auto;
        }
    }
}