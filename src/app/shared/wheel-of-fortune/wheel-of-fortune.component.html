<div class="wrapper">
     @if(wheelOfFortuneConfig()?.header){
        <app-wheel-dynamic-text [data]="wheelOfFortuneConfig()!.header" class="wheel-header"></app-wheel-dynamic-text>
     }

     @if(wheelOfFortuneConfig()?.description){
        <app-wheel-dynamic-text [data]="wheelOfFortuneConfig()!.description!" class="wheel-description"></app-wheel-dynamic-text>
     }


    @if(showSpinMarket()){
        <div class="spin-market">
            @for (item of mockBonuses; track $index) {
                <app-wheel-bonus-card [data]="item"></app-wheel-bonus-card>
            }
        </div>
    }
    @else {
        @if(showTrophy()){
            <div class="wheel-result">
                <app-wheel-trophy [data]="wheelTrophy()"></app-wheel-trophy>
            </div>
            }
            @else {
                <div class="wheel-container">
                    <div class="static-screen">
                        <img src="assets/icons/wheel-arrow.svg" alt="" [ngClass]="[!wheelState()?.spins && 'grayscale-filter', 'wheel-arrow']">
                    </div>
                <canvas #wheelCanvas class="wheel__canvas"></canvas>
                
            </div>    
            }
    }

   
    @if(wheelOfFortuneConfig()?.subtitle){
        <app-wheel-dynamic-text [data]="wheelOfFortuneConfig()!.subtitle!" class="wheel-subtitle"></app-wheel-dynamic-text>
    }

    <div class="wheel-buttons" [ngClass]="[showSpinMarket()?'mt-auto pb-40':'']">
        @for (button of wheelOfFortuneConfig()?.buttons; track button.action) {
    
        @if(button.action === WHEEL_BUTTON_ACTION.AUTHORIZE){
            <ng-content select=".wheel-login"></ng-content>
        }
        @else {
            <app-wheel-button [data]="button" (click)="wheelClickHandler(button.action)" [disabled]="isLoading()"></app-wheel-button>
        }
        }
        @if(showWheelGuy()){
            <img src="assets/bonus-guy.png" width="120px" class="wheel-guy"/>
        }
        
    </div>
</div>
