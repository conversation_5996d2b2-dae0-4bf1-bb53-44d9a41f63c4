import { AfterViewInit, Component, computed, ElementRef, inject, input, signal, ViewChild } from '@angular/core';
import { WheelAction, WheelSegment } from './models/wheel-of-fortune.model';
import { WheelOfFortuneService } from './services/wheel-of-fortune.service';
import { mockBonuses, wheelSegmentInput } from './data/wheel-of-fortune.config';
import { CommonModule } from '@angular/common';
import { finalize, take, tap, timer } from 'rxjs';
import { WheelButtonComponent } from "./components/wheel-button/wheel-button.component";
import { WheelTrophyComponent } from "./components/wheel-trophy/wheel-trophy.component";
import { WheelBonusCardComponent } from './components/wheel-bonus-card/wheel-bonus-card.component';
import { WheelDynamicTextComponent } from "./components/wheel-dynamic-text/wheel-dynamic-text.component";

@Component({
  selector: 'app-wheel-of-fortune',
  standalone: true,
  imports: [CommonModule, WheelButtonComponent, WheelTrophyComponent, WheelBonusCardComponent, WheelDynamicTextComponent],
  templateUrl: './wheel-of-fortune.component.html',
  styleUrl: './wheel-of-fortune.component.scss'
})
export class WheelOfFortuneComponent implements AfterViewInit{
  @ViewChild('wheelCanvas', { static: false }) canvasRef!: ElementRef;
  @ViewChild('wheel', { static: false }) wheel!: ElementRef<HTMLImageElement>;
  
  private readonly wheelService = inject(WheelOfFortuneService);

  private isSpinning = false;

  private ctx!: CanvasRenderingContext2D;

  private currentAngle = 0;

  protected readonly WHEEL_BUTTON_ACTION = WheelAction;

  protected readonly mockBonuses = mockBonuses;

  public wheelOfFortuneConfig = this.wheelService.currentWheelConfig;

  public showTrophy = signal<boolean>(false);

  public showSpinMarket = this.wheelService.showSpinMarket;

  public wheelTrophy = computed(() => this.showTrophy() ? this.wheelService.currentWheelTrophy() : null);
  
  public showWheelGuy = computed(() => !this.showTrophy() && !this.showSpinMarket());

  public wheelState = this.wheelService.currentWheelState;

  public wheelSegments = input<WheelSegment[]>(wheelSegmentInput);
  
  public isLoading = signal(false);

  ngAfterViewInit(): void{
    if (this.wheelSegments().length && !this.showTrophy() && this.canvasRef) {
      this.drawWheel();
      this.startContinuousSpin(.1);
    }
  }

   public wheelClickHandler(action:WheelAction) {
     this.wheelService.handleWheelAction(action);
     
     if (action === WheelAction.NAVIGATE_TO_WHEEL) {
       this.showTrophy.set(false);
       this.startContinuousSpin(.1);
     }

     if (action === WheelAction.SPIN) {
       this.isLoading.set(true);
       this.spinWheel();
     }
  }

    private drawWheel() {
      const canvas = this.canvasRef!.nativeElement as HTMLCanvasElement;
        
      this.ctx = canvas.getContext('2d')!;//creates a 2d context;
      const rimWidth = 10;
      canvas.width = 300 + 2*rimWidth;
      canvas.height = 300 + 2*rimWidth;
      
      const center = canvas.width / 2;
      const radius = center - 2*rimWidth;//radius of the inner circle;
      const anglePerSegment = (2 * Math.PI) / this.wheelSegments().length;
      let startAngle = -Math.PI / 2;

      this.applyGrayScaleFilter();
    
      // Draw the rim
      this.ctx.save();
      this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
      this.ctx.shadowBlur = 10;
      this.ctx.shadowOffsetX = 2;
      this.ctx.shadowOffsetY = 2;
      this.ctx.lineWidth = rimWidth;
      const outerRimRadius = radius + rimWidth;
      this.ctx.arc(center, center, outerRimRadius, 0, Math.PI * 2);
      this.ctx.fillStyle = '#C74C4D'; // Dark red rim
      this.ctx.fill();
      this.ctx.restore();

      this.wheelSegments().forEach((segment) => {
        const midAngle = startAngle + anglePerSegment / 2;
      
        // Draw segment
      this.ctx.beginPath();
      this.ctx.moveTo(center, center);
      this.ctx.arc(center, center, radius, startAngle, startAngle + anglePerSegment);
      const { x: midArcX, y: midArcY } = this.getMidArcPoint(center, center, radius, startAngle, anglePerSegment);
      const gradient = this.ctx.createLinearGradient(center, center, midArcX, midArcY);
      gradient.addColorStop(0, segment.color);
      gradient.addColorStop(1, segment.color === 'grey'? 'white':'brown');
      this.ctx.fillStyle = gradient;
      this.ctx.fill();
      this.ctx.lineWidth = 3;
      this.ctx.strokeStyle = 'white';
      this.ctx.stroke();

      const { textPosition: { x: textX, y: textY }, imagePosition: { x: imgX, y: imgY }} = this.getSegmentTextImagePosition(
        center, center, radius, startAngle, anglePerSegment, Boolean(segment.imgSrc), Boolean(segment.text)
      );

           /** Draw text */
      if (segment.text) {
        this.ctx.save();
        this.ctx.translate(textX, textY);
        this.ctx.rotate(midAngle + Math.PI / 2);
        this.ctx.fillStyle = 'black';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(segment.text, 0, 40);
        this.ctx.restore();
      }

      /** Draw image */
      if (segment.imgSrc) {
        const img = new Image();
        img.src = segment.imgSrc;

          const imgSize = 40;
          this.ctx.save();
          this.ctx.translate(imgX, imgY);
          this.ctx.rotate(midAngle + Math.PI / 2);

          this.ctx.drawImage(img, -imgSize / 2, -imgSize / 2, imgSize, imgSize);
          this.ctx.restore();
      }
    
        startAngle += anglePerSegment;
      });
      

    /** Draw the central piece. Move to the end of a method to avoid overwrites */
      this.drawCentralPiece(center);    
  }

  private getMidArcPoint(centerX: number, centerY: number, radius: number, startAngle: number, anglePerSegment: number) {
    const midAngle = startAngle + anglePerSegment / 2;

    const midX = centerX + Math.cos(midAngle) * radius;
    const midY = centerY + Math.sin(midAngle) * radius;

    return { x: midX, y: midY };
  }
  
  private getSegmentTextImagePosition(
    centerX: number,
    centerY: number,
    radius: number,
    startAngle: number,
    anglePerSegment: number,
    hasImage: boolean,
    hasText: boolean
) {
    const { x: midX, y: midY } = this.getMidArcPoint(centerX, centerY, radius * 0.8, startAngle, anglePerSegment); // Slightly inside the arc

    let textPosition = { x: midX, y: midY };
    let imagePosition = { x: midX, y: midY }

    return { textPosition, imagePosition};
}

  private drawCentralPiece(center: number) {
      this.ctx.beginPath();
      this.ctx.arc(center, center, 15, 0, Math.PI * 2);
      this.ctx.fillStyle = 'white';
      this.ctx.fill();
      this.ctx.closePath();
  }

  private applyGrayScaleFilter():void {
    !this.wheelService.currentWheelState()?.spins && (this.ctx.filter = 'grayscale(.6)');
  }

  private spinWheel() {
    this.startContinuousSpin();
    this.wheelService.requestWheelSpinResult()
    .pipe(
      tap(({ data }) => {
        if (data && data.trophy) {
          const segmentIndex = this.wheelSegments().findIndex(segment => segment.text === data.trophy?.text);
          const segmentAngle = (segmentIndex / this.wheelSegments().length) * 360;

          // Ensure the winning segment lands at the top (0 degrees)
          const centerAngle = segmentAngle + (360 / this.wheelSegments().length) / 2;

          const totalRotation = this.currentAngle + (360 * 5 + (360 - centerAngle));

          // Stop the continuous spin and slow down
          this.stopContinuousSpin(totalRotation);
        }
      }),
      finalize(()=>this.isLoading.set(false))
    ).subscribe();
  }

  private startContinuousSpin(defaultVelocity?:number) {
  this.isSpinning = true;
  let lastTime = performance.now();
  let velocity = defaultVelocity || 20; // Fast initial speed

  const spinLoop = (time: number) => {
    if (!this.isSpinning) return; // Stop loop when response arrives

    const deltaTime = time - lastTime;
    this.currentAngle += velocity * (deltaTime / 16); // Scale velocity based on frame time

    this.renderWheel();
    this.ctx.canvas.style.transform = `rotate(${this.currentAngle.toFixed(2)}deg)`;

    lastTime = time;
    requestAnimationFrame(spinLoop);
  };

  requestAnimationFrame(spinLoop);
  }
  
private stopContinuousSpin(targetAngle: number) {
    this.isSpinning = false; // Signal to stop continuous spin

    // Gradually slow down the spin instead of instantly stopping
    let start = performance.now();
    const duration = 3000; // Slow down duration
    let initialAngle = this.currentAngle;

    const animate = (time: number) => {
    const elapsed = time - start;
    let progress = Math.min(elapsed / duration, 1);

    // Decelerate smoothly (quadratic easing out)
    let easedProgress = 1 - Math.pow(1 - progress, 3);

    this.currentAngle = initialAngle + easedProgress * (targetAngle - initialAngle);
    this.renderWheel();
    this.ctx.canvas.style.transform = `rotate(${this.currentAngle.toFixed(2)}deg)`;

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      console.log("Spin complete!");
      timer(1000).pipe(take(1)).subscribe(() => {
        this.showTrophy.set(true);
        const prevState = this.wheelService.currentWheelState();
        if (prevState) {
          const { spins, bonuses } = prevState
          this.wheelService.updateWheelConfig(true, spins, bonuses, Boolean(this.wheelService.currentWheelTrophy()))
        }
        
      });
    }
  };
  requestAnimationFrame(animate);
}

  private renderWheel() {
    const canvas = this.canvasRef.nativeElement as HTMLCanvasElement;
    const ctx = this.ctx;
    const center = canvas.width / 2;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.save();
    ctx.translate(center, center);
    ctx.rotate((this.currentAngle * Math.PI) / 180);
    ctx.translate(-center, -center);
    this.drawWheel();
    ctx.restore();
  }
}
