import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { WheelGenericResponse, WheelSegment, WheelSpinResponse } from '../models/wheel-of-fortune.model';
import { map, Observable, timer } from 'rxjs';
import { mockResponseWithNoSpins, mockResponseWithSpins } from '../data/wheel-of-fortune.config';

@Injectable({
  providedIn: 'root'
})
export class WheelOfFortuneApiService {

  private http = inject(HttpClient);

  getUserWheelState() {
    
  }

  public getSpinResult():Observable<WheelGenericResponse<WheelSpinResponse>> {
    return timer(5000)
      .pipe(
        map(() => ({success:true, data:mockResponseWithNoSpins}) )
      )
  }

  buyWheelSpins() {
    
  }
}
