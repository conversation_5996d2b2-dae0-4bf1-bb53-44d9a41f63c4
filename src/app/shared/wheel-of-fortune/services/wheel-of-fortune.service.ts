import { effect, inject, Injectable, Signal, signal } from '@angular/core';
import { WheelAction, WheelButtonConfig, WheelConfig, WheelSegment, WheelUserState } from '../models/wheel-of-fortune.model';
import { wheelOfFortuneConfig, wheelTextClassMaker } from '../data/wheel-of-fortune.config';
import { Router } from '@angular/router';
import { filter, map, tap } from 'rxjs';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { WheelOfFortuneApiService } from './wheel-of-fortune-api.service';


@Injectable({
  providedIn: 'root'
})
export class WheelOfFortuneService {
  private router = inject(Router);

  private apiService = inject(WheelOfFortuneApiService);

  private wheelConfig = signal<WheelConfig | null>(null);

  private wheelTrophy = signal<WheelSegment | null>(null);

  private trophyStateTracker = signal<WheelUserState | null>(null);

  private wheelModalRef: NgbModalRef | null = null;

  private _showSpinMarket = signal(false);

  constructor() {
    effect(() => {
      if (this._showSpinMarket()) {
        this.buySpinsConfig();
      }
    }, {allowSignalWrites: true})
  }

  get currentWheelConfig():Signal<WheelConfig|null> {
    return this.wheelConfig.asReadonly();
  }

  get currentWheelTrophy(): Signal<WheelSegment | null>{
    return this.wheelTrophy.asReadonly();
  }

  get currentWheelState(): Signal<WheelUserState | null> {
    return this.trophyStateTracker.asReadonly();
  }

  get showSpinMarket() {
   return this._showSpinMarket.asReadonly();
  }

  private buyWheelSpins() {
    /**make http req to buy spins. update the wheel config based on the response*/
  }
  
  private buySpinsConfig(): void {
    const returnButton:WheelButtonConfig = { action: WheelAction.NAVIGATE_TO_WHEEL, text: 'До колеса', icon:'assets/icons/return.svg', type: 'secondary' }
      this.wheelConfig.set({
        header: {
          text: 'Доступно бонусів',
          translateKeyValuePairs: {},
          classes: wheelTextClassMaker('header')
        },
        buttons: [returnButton] 
    })
    }

  public setWheelModalRef(ref:NgbModalRef | null): void {
    this.wheelModalRef = ref;
  }

  public updateWheelConfig(userAuthorized=false,spinsLeft: number, bonusesLeft: number, won=false): void {
    const updatedConfig = wheelOfFortuneConfig(userAuthorized, spinsLeft, bonusesLeft, won);
    this.wheelConfig.set(updatedConfig);
  }

  public handleWheelAction(action:WheelAction): void {
    switch (action) {
      case WheelAction.AUTHORIZE:
        break;
      case WheelAction.BUY_SPINS:
        this._showSpinMarket.set(true);
        this.wheelTrophy.set(null);
        break;
      case WheelAction.ORDER:
      case WheelAction.NAVIGATE_TO_HOME:
        this.wheelModalRef?.close();
        this.router.navigate(['/home']);
        break;
      case WheelAction.NAVIGATE_TO_WHEEL:
        this.setWheelTrophy(null);
        this._showSpinMarket.set(false);

        const spins = this.trophyStateTracker()?.spins || 0;
        const bonuses = this.trophyStateTracker()?.bonuses || 0;
        this.updateWheelConfig(true, spins, bonuses);
        break;
      case WheelAction.SPIN:
        break;
      default:
        break;
    }
  }

  public setWheelTrophy(trophy:WheelSegment|null): void {
    this.wheelTrophy.set(trophy);
  }

  public requestWheelSpinResult() {
    /* 
    1. make a request with selected segment 
      data:{
        spins:1,
        bonuses:0,
        trophy:{
          type:'bonuses' | 'free product',
          daysTillExpiration:number,
          text:string;
          imgSrc: string (expect img url from back if type=free product,
            use static image for bonuses)
        }|null
      }
    2. request updated wheelConfig;
    3. display changes
    */
    return this.apiService.getSpinResult()
      .pipe(
        filter(resp => resp.success),
        tap(({ data }) => {
          if (data) {
            const { spins, bonuses, trophy } = data;
            this.setWheelTrophy(trophy);
            // this.updateWheelConfig(true, spins, bonuses, Boolean(trophy));
            this.trophyStateTracker.set({ spins, bonuses });
          }
        }),
        map(({ data }) => ({data}))
      );
  }

  public updateWheelModalRef(ref: NgbModalRef | null) {
    this.wheelModalRef = ref;
  }
}
