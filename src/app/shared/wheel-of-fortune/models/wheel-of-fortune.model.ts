export enum WheelAction {
    AUTHORIZE = 1,
    SPIN,
    ORDER,
    NAVIGATE_TO_HOME,
    NAVIGATE_TO_WHEEL,
    BUY_SPINS
}

export interface WheelConfig {
    header: WheelDynamicText;
    description?: WheelDynamicText;
    subtitle?: WheelDynamicText;
    buttons: WheelButtonConfig[]
}

export interface WheelButtonConfig {
    action: WheelAction;
    text?: string;
    icon?: string;
    type?:'primary' | 'secondary'
}

export interface WheelSegment {
    type: 'bonuses' | 'free_product',
    text?: string;
    imgSrc: string;
    daysTillExpiration?: number;
    color: string;
}

export interface WheelUserState {
    spins: number;
    bonuses: number;
}

export interface WheelSpinResponse {
    spins: number;
    bonuses: number;
    trophy: WheelSegment | null;
}

export interface WheelGenericResponse <T> {
    success: boolean;
    data?:T
}

export interface WheelBonus {
    amount: number;
    spinsCanBuy: number;
    formerPrice?: number;
    discountPercentage?: number;
}

export interface WheelDynamicText {
    text?: string;
    html?: string;
    translateKeyValuePairs: {
        [prop:string]:string | number
    },
    classes: string[];
}