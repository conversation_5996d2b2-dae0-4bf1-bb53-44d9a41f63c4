:host {
    color: var(--text-primary);

    .wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .wheel-header{
        display: block;
        text-align: center;
        margin-bottom: 30px;
    }

    .wheel-description {
        display: block;
        margin-bottom: 30px;
    }

    .wheel-subtitle {
        display: block;
        margin-bottom: 12px;
    }

    .wheel-buttons {
        display: flex;
        gap: 14px;
        flex-wrap: wrap;
        position: relative;

        .wheel-guy {
            position: absolute;
            top: -94px;
            right: 20px;
            
            @media screen and (min-width:768px) {
                right: 70px;
            }
        }
    }

    .wheel {
        width: 100%;
        transition: transform 4s cubic-bezier(0.25, 1, 0.5, 1);
        transform-origin: center;

        &__canvas {
            display: block;
            margin-inline: auto;
        }
    }

    .wheel-container {
        position: relative;
        margin-inline: auto;
    
        .static-screen{
            width: 100%;
            height: 100%;
            position:absolute;
            top: 0;
            z-index: 100;
        }
    
        .wheel-arrow {
            position: absolute; 
            top: 0; 
            left: 50%;
            transform: translateX(-50%);
        }
    
    }

    .spin-market {
        display: flex;
        gap: 14px;
        padding-bottom: 40px;
    }

    .grayscale-filter {
        filter: grayscale(.6);
    }
}

.mt-auto {
    margin-top: auto;
}

.pb-40 {
    padding-bottom: 40px;
}