export interface CityDynamicSettingsDTO {
    success: boolean
    data: Data
}

export interface Data {
    global: {
        fortune_wheel: FortuneWheelGlobalConfig
    },
    city: {
        fortune_wheel: FortuneWheelCityConfig
        future_ordering: FutureOrdering
    }
}

export interface FortuneWheelGlobalConfig {
  registration_spins: number
  order_spins: number
  order_spins_expiry_days: number
  spin_cost: number
}

export interface FortuneWheelCityConfig {
  enabled: boolean
  product_prize_expiry_days: number
}

export interface FutureOrdering {
  disabled_dates: any[]
  days_ahead: number
  enabled: boolean
}