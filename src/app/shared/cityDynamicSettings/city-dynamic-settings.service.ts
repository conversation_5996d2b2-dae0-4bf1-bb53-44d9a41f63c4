import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { StorageLogicService } from '../utils/services/storage-logic.service';
import { AppApiUrls } from 'src/app/app.api.urls';
import { Observable } from 'rxjs';
import { CityDynamicSettingsDTO } from './city-dynamic-settings.model';

@Injectable({
  providedIn: 'root'
})
export class CityDynamicSettingsService {
  private http = inject(HttpClient);
  private readonly storageService = inject(StorageLogicService);

  public getCityDynamicSettings(): Observable<CityDynamicSettingsDTO> {
    const cityId = this.storageService.getData('cityInfo')?.id;
    return this.http.get<CityDynamicSettingsDTO>(AppApiUrls.cityDynamicConfig(cityId), { headers: { skipInterceptor: 'true' } })
  }
}
