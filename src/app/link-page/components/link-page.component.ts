import {Component, OnInit} from '@angular/core';
import {Unsubscribe} from "../../unsubscribe";
import {<PERSON><PERSON>} from "../../shell/components/navbar/interfaces/navbar.interface";
import {StorageLogicService} from 'src/app/shared/utils/services/storage-logic.service';

@Component({
  selector: 'app-link-page',
  templateUrl: './link-page.component.html',
  styleUrls: ['./link-page.component.scss']
})
export class LinkPageComponent extends Unsubscribe implements OnInit {

  appModalLangGoogle: string = Langs.en
  appModalLangApple: string = Langs.en
  appModalLangScreen: string = Langs.ua
  constructor(
    public storageService: StorageLogicService,
  )
  {
    super()
  }

  ngOnInit(): void {
    if (this.storageService.getData('city_name')) {
      this.detectAppModalLang()
    }
  }

  detectAppModalLang(): void {
    const lang = this.storageService.getData('lang')
    const country =this.storageService.getData('country').name

    if (country === 'Україна') {
      this.appModalLangScreen = Langs.ua
    } else {
      this.appModalLangScreen = Langs.es
    }

    switch (lang) {
      case Langs.es:
        this.appModalLangGoogle = Langs.es
        this.appModalLangApple = Langs.es
        break
      case Langs.en:
        this.appModalLangGoogle = Langs.en
        this.appModalLangApple = Langs.en
        break
      case Langs.ua:
        this.appModalLangGoogle = Langs.ua
        this.appModalLangApple = Langs.en
        break
      case Langs.ru:
        this.appModalLangGoogle = Langs.ru
        this.appModalLangApple = Langs.ru
        break
    }
  }

  openApp(url: string): void {
    window.open(url, '_blank')
  }

  protected readonly window = window;
}
