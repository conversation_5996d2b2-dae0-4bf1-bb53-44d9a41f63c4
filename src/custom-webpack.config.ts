// import { sentryWebpackPlugin } from "@sentry/webpack-plugin";
// import { sentryConfig } from "src/environments/sentry-config";

module.exports = {
  devtool: "source-map",

  plugins: [
    // sentryWebpackPlugin({
    //   org: "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    //   project: "3303-site-test",
    //   authToken: sentryConfig.authToken,
    // }),
    // sentryWebpackPlugin({
    //   applicationKey: "sushi-custom",
    //   org: 'moskalenko',
    //   project: 'site-sushi-3303',
    //   authToken:sentryConfig.authToken,
    //   release: {
    //     name:sentryConfig.release,
    //   }
    // }),
  ],
};
