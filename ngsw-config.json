{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["./favicon.ico", "./index.html", "./*.css", "./*.js", "./assets/manifest/manifest.webmanifest.json"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/**/*.(svg|cur|jpg|jpeg|png|apng|webp|avif|gif|otf|ttf|woff|woff2)"], "urls": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.3**"]}}, {"name": "translate", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["./assets/i18n/*.json"]}}], "dataGroups": [{"name": "restricted-caching", "urls": ["https://storage.3303.ua/api/app/settings?city_id=**"], "cacheConfig": {"maxAge": "0u", "maxSize": 0, "strategy": "freshness"}}, {"name": "api-product", "urls": ["https://storage.dev.3303.ua/api/app/**"], "cacheConfig": {"maxAge": "7h", "maxSize": 500, "strategy": "freshness", "timeout": "3s"}}, {"name": "api-images", "urls": ["https://storage.dev.3303.ua/assets/3303/crmimages/**"], "cacheQueryOptions": {"ignoreSearch": true}, "cacheConfig": {"cacheOpaqueResponses": true, "maxSize": 2000, "maxAge": "7h", "strategy": "freshness", "timeout": "3s"}}]}